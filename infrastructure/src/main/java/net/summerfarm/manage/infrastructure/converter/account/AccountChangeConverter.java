package net.summerfarm.manage.infrastructure.converter.account;

import net.summerfarm.manage.infrastructure.model.account.AccountChange;
import net.summerfarm.manage.domain.account.entity.AccountChangeEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @date 2023-10-26 16:20:19
 * @version 1.0
 *
 */
public class AccountChangeConverter {

    private AccountChangeConverter() {
        // 无需实现
    }




    public static List<AccountChangeEntity> toAccountChangeEntityList(List<AccountChange> accountChangeList) {
        if (accountChangeList == null) {
            return Collections.emptyList();
        }
        List<AccountChangeEntity> accountChangeEntityList = new ArrayList<>();
        for (AccountChange accountChange : accountChangeList) {
            accountChangeEntityList.add(toAccountChangeEntity(accountChange));
        }
        return accountChangeEntityList;
}


    public static AccountChangeEntity toAccountChangeEntity(AccountChange accountChange) {
        if (accountChange == null) {
             return null;
        }
        AccountChangeEntity accountChangeEntity = new AccountChangeEntity();
        accountChangeEntity.setId(accountChange.getId());
        accountChangeEntity.setMId(accountChange.getMId());
        accountChangeEntity.setAccountId(accountChange.getAccountId());
        accountChangeEntity.setOldPhone(accountChange.getOldPhone());
        accountChangeEntity.setOldContact(accountChange.getOldContact());
        accountChangeEntity.setNewPhone(accountChange.getNewPhone());
        accountChangeEntity.setNewContact(accountChange.getNewContact());
        accountChangeEntity.setMname(accountChange.getMname());
        accountChangeEntity.setRemark(accountChange.getRemark());
        accountChangeEntity.setStatus(accountChange.getStatus());
        accountChangeEntity.setOpenid(accountChange.getOpenid());
        accountChangeEntity.setUnionid(accountChange.getUnionid());
        accountChangeEntity.setCreateTime(accountChange.getCreateTime());
        return accountChangeEntity;
    }






    public static List<AccountChange> toAccountChangeList(List<AccountChangeEntity> accountChangeEntityList) {
        if (accountChangeEntityList == null) {
            return Collections.emptyList();
        }
        List<AccountChange> accountChangeList = new ArrayList<>();
        for (AccountChangeEntity accountChangeEntity : accountChangeEntityList) {
            accountChangeList.add(toAccountChange(accountChangeEntity));
        }
        return accountChangeList;
    }


    public static AccountChange toAccountChange(AccountChangeEntity accountChangeEntity) {
        if (accountChangeEntity == null) {
            return null;
        }
        AccountChange accountChange = new AccountChange();
        accountChange.setId(accountChangeEntity.getId());
        accountChange.setMId(accountChangeEntity.getMId());
        accountChange.setAccountId(accountChangeEntity.getAccountId());
        accountChange.setOldPhone(accountChangeEntity.getOldPhone());
        accountChange.setOldContact(accountChangeEntity.getOldContact());
        accountChange.setNewPhone(accountChangeEntity.getNewPhone());
        accountChange.setNewContact(accountChangeEntity.getNewContact());
        accountChange.setMname(accountChangeEntity.getMname());
        accountChange.setRemark(accountChangeEntity.getRemark());
        accountChange.setStatus(accountChangeEntity.getStatus());
        accountChange.setOpenid(accountChangeEntity.getOpenid());
        accountChange.setUnionid(accountChangeEntity.getUnionid());
        accountChange.setCreateTime(accountChangeEntity.getCreateTime());
        return accountChange;
    }
}
