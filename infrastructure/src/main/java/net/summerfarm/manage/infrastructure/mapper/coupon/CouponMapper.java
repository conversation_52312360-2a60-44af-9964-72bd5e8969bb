package net.summerfarm.manage.infrastructure.mapper.coupon;

import net.summerfarm.manage.infrastructure.model.coupon.Coupon;
import net.summerfarm.manage.domain.coupon.param.query.CouponQueryParam;
import net.summerfarm.manage.domain.coupon.entity.CouponEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-12-19 13:49:12
 * @version 1.0
 *
 */
@Mapper
public interface CouponMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(Coupon record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(Coupon record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    Coupon selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<Coupon> selectByCondition(CouponQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<CouponEntity> getPage(CouponQueryParam param);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<Coupon> selectByIds(@Param("ids")List<Long> ids);

}

