package net.summerfarm.manage.infrastructure.repository.coupon;


import net.summerfarm.manage.infrastructure.model.coupon.MerchantCoupon;
import net.summerfarm.manage.infrastructure.mapper.coupon.MerchantCouponMapper;
import net.summerfarm.manage.infrastructure.converter.coupon.MerchantCouponConverter;
import net.summerfarm.manage.domain.coupon.repository.MerchantCouponQueryRepository;
import net.summerfarm.manage.domain.coupon.entity.MerchantCouponEntity;
import net.summerfarm.manage.domain.coupon.param.query.MerchantCouponQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-05-31 15:37:46
* @version 1.0
*
*/
@Repository
public class MerchantCouponQueryRepositoryImpl implements MerchantCouponQueryRepository {

    @Autowired
    private MerchantCouponMapper merchantCouponMapper;


    @Override
    public PageInfo<MerchantCouponEntity> getPage(MerchantCouponQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<MerchantCouponEntity> entities = merchantCouponMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public MerchantCouponEntity selectById(Long id) {
        return MerchantCouponConverter.toMerchantCouponEntity(merchantCouponMapper.selectById(id));
    }


    @Override
    public List<MerchantCouponEntity> selectByCondition(MerchantCouponQueryParam param) {
        return MerchantCouponConverter.toMerchantCouponEntityList(merchantCouponMapper.selectByCondition(param));
    }

}