package net.summerfarm.manage.infrastructure.converter.merchant;

import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantCommandParam;
import net.summerfarm.manage.infrastructure.model.merchant.Merchant;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-09-19 13:44:23
 * @version 1.0
 *
 */
public class MerchantConverter {


    private MerchantConverter() {
        // 无需实现
    }

    public static List<Merchant> toMerchantList(List<MerchantEntity> merchantEntityList) {
        if (merchantEntityList == null) {
            return Collections.emptyList();
        }
        List<Merchant> merchantList = new ArrayList<>();
        for (MerchantEntity merchantEntity : merchantEntityList) {
            merchantList.add(toMerchant(merchantEntity));
        }
        return merchantList;
    }

    public static Merchant toMerchant(MerchantEntity merchantEntity) {
        if (merchantEntity == null) {
            return null;
        }
        Merchant merchant = new Merchant();
        merchant.setMId(merchantEntity.getMId());
        merchant.setRoleId(merchantEntity.getRoleId());
        merchant.setMname(merchantEntity.getMname());
        merchant.setMcontact(merchantEntity.getMcontact());
        merchant.setOpenid(merchantEntity.getOpenid());
        merchant.setPhone(merchantEntity.getPhone());
        merchant.setIslock(merchantEntity.getIslock());
        merchant.setRankId(merchantEntity.getRankId());
        merchant.setRegisterTime(merchantEntity.getRegisterTime());
        merchant.setLoginTime(merchantEntity.getLoginTime());
        merchant.setInvitecode(merchantEntity.getInvitecode());
        merchant.setChannelCode(merchantEntity.getChannelCode());
        merchant.setInviterChannelCode(merchantEntity.getInviterChannelCode());
        merchant.setAuditTime(merchantEntity.getAuditTime());
        merchant.setAuditUser(merchantEntity.getAuditUser());
        merchant.setBusinessLicense(merchantEntity.getBusinessLicense());
        merchant.setProvince(merchantEntity.getProvince());
        merchant.setCity(merchantEntity.getCity());
        merchant.setArea(merchantEntity.getArea());
        merchant.setAddress(merchantEntity.getAddress());
        merchant.setPoiNote(merchantEntity.getPoiNote());
        merchant.setRemark(merchantEntity.getRemark());
        merchant.setShopSign(merchantEntity.getShopSign());
        merchant.setOtherProof(merchantEntity.getOtherProof());
        merchant.setLastOrderTime(merchantEntity.getLastOrderTime());
        merchant.setAreaNo(merchantEntity.getAreaNo());
        merchant.setSize(merchantEntity.getSize());
        merchant.setType(merchantEntity.getType());
        merchant.setTradeArea(merchantEntity.getTradeArea());
        merchant.setTradeGroup(merchantEntity.getTradeGroup());
        merchant.setUnionid(merchantEntity.getUnionid());
        merchant.setMpOpenid(merchantEntity.getMpOpenid());
        merchant.setAdminId(merchantEntity.getAdminId());
        merchant.setDirect(merchantEntity.getDirect());
        merchant.setServer(merchantEntity.getServer());
        merchant.setPopView(merchantEntity.getPopView());
        merchant.setMemberIntegral(merchantEntity.getMemberIntegral());
        merchant.setGrade(merchantEntity.getGrade());
        merchant.setSkuShow(merchantEntity.getSkuShow());
        merchant.setRechargeAmount(merchantEntity.getRechargeAmount());
        merchant.setCashAmount(merchantEntity.getCashAmount());
        merchant.setCashUpdateTime(merchantEntity.getCashUpdateTime());
        merchant.setShowPrice(merchantEntity.getShowPrice());
        merchant.setMergeAdmin(merchantEntity.getMergeAdmin());
        merchant.setMergeTime(merchantEntity.getMergeTime());
        merchant.setFirstLoginPop(merchantEntity.getFirstLoginPop());
        merchant.setChangePop(merchantEntity.getChangePop());
        merchant.setPullBlackRemark(merchantEntity.getPullBlackRemark());
        merchant.setPullBlackOperator(merchantEntity.getPullBlackOperator());
        merchant.setHouseNumber(merchantEntity.getHouseNumber());
        merchant.setCompanyBrand(merchantEntity.getCompanyBrand());
        merchant.setCluePool(merchantEntity.getCluePool());
        merchant.setMerchantType(merchantEntity.getMerchantType());
        merchant.setEnterpriseScale(merchantEntity.getEnterpriseScale());
        merchant.setUpdateTime(merchantEntity.getUpdateTime());
        merchant.setExamineType(merchantEntity.getExamineType());
        merchant.setDisplayButton(merchantEntity.getDisplayButton());
        merchant.setOperateStatus(merchantEntity.getOperateStatus());
        merchant.setUpdater(merchantEntity.getUpdater());
        merchant.setDoorPic(merchantEntity.getDoorPic());
        merchant.setPreRegisterFlag(merchantEntity.getPreRegisterFlag());
        return merchant;
    }

    public static Merchant toMerchant(MerchantCommandParam merchantCommandParam) {
        if (merchantCommandParam == null) {
            return null;
        }
        Merchant merchant = new Merchant();
        merchant.setMId(merchantCommandParam.getMId());
        merchant.setGrade(merchantCommandParam.getGrade());
        merchant.setMemberIntegral(merchantCommandParam.getMemberIntegral());
        return merchant;
    }

    public static List<MerchantEntity> toMerchantEntityList(List<Merchant> merchantList) {
        if (merchantList == null) {
            return Collections.emptyList();
        }
        List<MerchantEntity> merchantEntityList = new ArrayList<>();
        for (Merchant merchant : merchantList) {
            merchantEntityList.add(toMerchantEntity(merchant));
        }
        return merchantEntityList;
    }

    public static MerchantEntity toMerchantEntity(Merchant merchant) {
        if (merchant == null) {
            return null;
        }
        MerchantEntity merchantEntity = new MerchantEntity();
        merchantEntity.setMId(merchant.getMId());
        merchantEntity.setRoleId(merchant.getRoleId());
        merchantEntity.setMname(merchant.getMname());
        merchantEntity.setMcontact(merchant.getMcontact());
        merchantEntity.setOpenid(merchant.getOpenid());
        merchantEntity.setPhone(merchant.getPhone());
        merchantEntity.setIslock(merchant.getIslock());
        merchantEntity.setRankId(merchant.getRankId());
        merchantEntity.setRegisterTime(merchant.getRegisterTime());
        merchantEntity.setLoginTime(merchant.getLoginTime());
        merchantEntity.setInvitecode(merchant.getInvitecode());
        merchantEntity.setChannelCode(merchant.getChannelCode());
        merchantEntity.setInviterChannelCode(merchant.getInviterChannelCode());
        merchantEntity.setAuditTime(merchant.getAuditTime());
        merchantEntity.setAuditUser(merchant.getAuditUser());
        merchantEntity.setBusinessLicense(merchant.getBusinessLicense());
        merchantEntity.setProvince(merchant.getProvince());
        merchantEntity.setCity(merchant.getCity());
        merchantEntity.setArea(merchant.getArea());
        merchantEntity.setAddress(merchant.getAddress());
        merchantEntity.setPoiNote(merchant.getPoiNote());
        merchantEntity.setRemark(merchant.getRemark());
        merchantEntity.setShopSign(merchant.getShopSign());
        merchantEntity.setOtherProof(merchant.getOtherProof());
        merchantEntity.setLastOrderTime(merchant.getLastOrderTime());
        merchantEntity.setAreaNo(merchant.getAreaNo());
        merchantEntity.setSize(merchant.getSize());
        merchantEntity.setType(merchant.getType());
        merchantEntity.setTradeArea(merchant.getTradeArea());
        merchantEntity.setTradeGroup(merchant.getTradeGroup());
        merchantEntity.setUnionid(merchant.getUnionid());
        merchantEntity.setMpOpenid(merchant.getMpOpenid());
        merchantEntity.setAdminId(merchant.getAdminId());
        merchantEntity.setDirect(merchant.getDirect());
        merchantEntity.setServer(merchant.getServer());
        merchantEntity.setPopView(merchant.getPopView());
        merchantEntity.setMemberIntegral(merchant.getMemberIntegral());
        merchantEntity.setGrade(merchant.getGrade());
        merchantEntity.setSkuShow(merchant.getSkuShow());
        merchantEntity.setRechargeAmount(merchant.getRechargeAmount());
        merchantEntity.setCashAmount(merchant.getCashAmount());
        merchantEntity.setCashUpdateTime(merchant.getCashUpdateTime());
        merchantEntity.setShowPrice(merchant.getShowPrice());
        merchantEntity.setMergeAdmin(merchant.getMergeAdmin());
        merchantEntity.setMergeTime(merchant.getMergeTime());
        merchantEntity.setFirstLoginPop(merchant.getFirstLoginPop());
        merchantEntity.setChangePop(merchant.getChangePop());
        merchantEntity.setPullBlackRemark(merchant.getPullBlackRemark());
        merchantEntity.setPullBlackOperator(merchant.getPullBlackOperator());
        merchantEntity.setHouseNumber(merchant.getHouseNumber());
        merchantEntity.setCompanyBrand(merchant.getCompanyBrand());
        merchantEntity.setCluePool(merchant.getCluePool());
        merchantEntity.setMerchantType(merchant.getMerchantType());
        merchantEntity.setEnterpriseScale(merchant.getEnterpriseScale());
        merchantEntity.setUpdateTime(merchant.getUpdateTime());
        merchantEntity.setExamineType(merchant.getExamineType());
        merchantEntity.setDisplayButton(merchant.getDisplayButton());
        merchantEntity.setOperateStatus(merchant.getOperateStatus());
        merchantEntity.setUpdater(merchant.getUpdater());
        merchantEntity.setDoorPic(merchant.getDoorPic());
        merchantEntity.setPreRegisterFlag(merchant.getPreRegisterFlag());
        merchantEntity.setBusinessLine(merchant.getBusinessLine());
        return merchantEntity;
    }
}
