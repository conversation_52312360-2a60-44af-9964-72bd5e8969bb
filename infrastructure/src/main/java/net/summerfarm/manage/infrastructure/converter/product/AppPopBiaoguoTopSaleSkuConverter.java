package net.summerfarm.manage.infrastructure.converter.product;

import net.summerfarm.manage.infrastructure.model.product.AppPopBiaoguoTopSaleSku;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoTopSaleSkuEntity;
import net.summerfarm.manage.domain.product.param.command.AppPopBiaoguoTopSaleSkuCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2024-11-18 15:55:40
 * @version 1.0
 *
 */
public class AppPopBiaoguoTopSaleSkuConverter {

    private AppPopBiaoguoTopSaleSkuConverter() {
        // 无需实现
    }




    public static List<AppPopBiaoguoTopSaleSkuEntity> toAppPopBiaoguoTopSaleSkuEntityList(List<AppPopBiaoguoTopSaleSku> appPopBiaoguoTopSaleSkuList) {
        if (appPopBiaoguoTopSaleSkuList == null) {
            return Collections.emptyList();
        }
        List<AppPopBiaoguoTopSaleSkuEntity> appPopBiaoguoTopSaleSkuEntityList = new ArrayList<>();
        for (AppPopBiaoguoTopSaleSku appPopBiaoguoTopSaleSku : appPopBiaoguoTopSaleSkuList) {
            appPopBiaoguoTopSaleSkuEntityList.add(toAppPopBiaoguoTopSaleSkuEntity(appPopBiaoguoTopSaleSku));
        }
        return appPopBiaoguoTopSaleSkuEntityList;
}


    public static AppPopBiaoguoTopSaleSkuEntity toAppPopBiaoguoTopSaleSkuEntity(AppPopBiaoguoTopSaleSku appPopBiaoguoTopSaleSku) {
        if (appPopBiaoguoTopSaleSku == null) {
             return null;
        }
        AppPopBiaoguoTopSaleSkuEntity appPopBiaoguoTopSaleSkuEntity = new AppPopBiaoguoTopSaleSkuEntity();
        appPopBiaoguoTopSaleSkuEntity.setId(appPopBiaoguoTopSaleSku.getId());
        appPopBiaoguoTopSaleSkuEntity.setCategoryName(appPopBiaoguoTopSaleSku.getCategoryName());
        appPopBiaoguoTopSaleSkuEntity.setCompetitor(appPopBiaoguoTopSaleSku.getCompetitor());
        appPopBiaoguoTopSaleSkuEntity.setSkuCode(appPopBiaoguoTopSaleSku.getSkuCode());
        appPopBiaoguoTopSaleSkuEntity.setSpiderFetchTime(appPopBiaoguoTopSaleSku.getSpiderFetchTime());
        appPopBiaoguoTopSaleSkuEntity.setFinalStandardPrice(appPopBiaoguoTopSaleSku.getFinalStandardPrice());
        appPopBiaoguoTopSaleSkuEntity.setMonthSale(appPopBiaoguoTopSaleSku.getMonthSale());
        appPopBiaoguoTopSaleSkuEntity.setUrl(appPopBiaoguoTopSaleSku.getUrl());
        appPopBiaoguoTopSaleSkuEntity.setGrossWeight(appPopBiaoguoTopSaleSku.getGrossWeight());
        appPopBiaoguoTopSaleSkuEntity.setNetWeight(appPopBiaoguoTopSaleSku.getNetWeight());
        appPopBiaoguoTopSaleSkuEntity.setSpecification(appPopBiaoguoTopSaleSku.getSpecification());
        appPopBiaoguoTopSaleSkuEntity.setGoodsName(appPopBiaoguoTopSaleSku.getGoodsName());
        appPopBiaoguoTopSaleSkuEntity.setSalesVolume3d(appPopBiaoguoTopSaleSku.getSalesVolume3d());
        appPopBiaoguoTopSaleSkuEntity.setMonthsaleGmv(appPopBiaoguoTopSaleSku.getMonthsaleGmv());
        appPopBiaoguoTopSaleSkuEntity.setCategoryLevel2(appPopBiaoguoTopSaleSku.getCategoryLevel2());
        appPopBiaoguoTopSaleSkuEntity.setMonthsaleGmv3dAgo(appPopBiaoguoTopSaleSku.getMonthsaleGmv3dAgo());
        appPopBiaoguoTopSaleSkuEntity.setTopMatchedXianmuSkuList(appPopBiaoguoTopSaleSku.getTopMatchedXianmuSkuList());
        appPopBiaoguoTopSaleSkuEntity.setCreateTime(appPopBiaoguoTopSaleSku.getCreateTime());
        appPopBiaoguoTopSaleSkuEntity.setDs(appPopBiaoguoTopSaleSku.getDs());
        appPopBiaoguoTopSaleSkuEntity.setCategory1(appPopBiaoguoTopSaleSku.getCategory1());
        appPopBiaoguoTopSaleSkuEntity.setCategory2(appPopBiaoguoTopSaleSku.getCategory2());
        appPopBiaoguoTopSaleSkuEntity.setCategory3(appPopBiaoguoTopSaleSku.getCategory3());
        return appPopBiaoguoTopSaleSkuEntity;
    }


    public static AppPopBiaoguoTopSaleSku toAppPopBiaoguoTopSaleSku(AppPopBiaoguoTopSaleSkuCommandParam param) {
        if (param == null) {
            return null;
        }
        AppPopBiaoguoTopSaleSku appPopBiaoguoTopSaleSku = new AppPopBiaoguoTopSaleSku();
        appPopBiaoguoTopSaleSku.setId(param.getId());
        appPopBiaoguoTopSaleSku.setCategoryName(param.getCategoryName());
        appPopBiaoguoTopSaleSku.setCompetitor(param.getCompetitor());
        appPopBiaoguoTopSaleSku.setSkuCode(param.getSkuCode());
        appPopBiaoguoTopSaleSku.setSpiderFetchTime(param.getSpiderFetchTime());
        appPopBiaoguoTopSaleSku.setFinalStandardPrice(param.getFinalStandardPrice());
        appPopBiaoguoTopSaleSku.setMonthSale(param.getMonthSale());
        appPopBiaoguoTopSaleSku.setUrl(param.getUrl());
        appPopBiaoguoTopSaleSku.setGrossWeight(param.getGrossWeight());
        appPopBiaoguoTopSaleSku.setNetWeight(param.getNetWeight());
        appPopBiaoguoTopSaleSku.setSpecification(param.getSpecification());
        appPopBiaoguoTopSaleSku.setGoodsName(param.getGoodsName());
        appPopBiaoguoTopSaleSku.setSalesVolume3d(param.getSalesVolume3d());
        appPopBiaoguoTopSaleSku.setMonthsaleGmv(param.getMonthsaleGmv());
        appPopBiaoguoTopSaleSku.setCategoryLevel2(param.getCategoryLevel2());
        appPopBiaoguoTopSaleSku.setMonthsaleGmv3dAgo(param.getMonthsaleGmv3dAgo());
        appPopBiaoguoTopSaleSku.setTopMatchedXianmuSkuList(param.getTopMatchedXianmuSkuList());
        appPopBiaoguoTopSaleSku.setCreateTime(param.getCreateTime());
        appPopBiaoguoTopSaleSku.setDs(param.getDs());
        return appPopBiaoguoTopSaleSku;
    }
}
