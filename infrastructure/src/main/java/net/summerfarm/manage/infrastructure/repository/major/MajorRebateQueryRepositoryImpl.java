package net.summerfarm.manage.infrastructure.repository.major;


import net.summerfarm.manage.infrastructure.model.major.MajorRebate;
import net.summerfarm.manage.infrastructure.mapper.major.MajorRebateMapper;
import net.summerfarm.manage.infrastructure.converter.major.MajorRebateConverter;
import net.summerfarm.manage.domain.major.repository.MajorRebateQueryRepository;
import net.summerfarm.manage.domain.major.entity.MajorRebateEntity;
import net.summerfarm.manage.domain.major.param.query.MajorRebateQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2025-02-27 15:22:25
* @version 1.0
*
*/
@Repository
public class MajorRebateQueryRepositoryImpl implements MajorRebateQueryRepository {

    @Autowired
    private MajorRebateMapper majorRebateMapper;


    @Override
    public PageInfo<MajorRebateEntity> getPage(MajorRebateQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<MajorRebateEntity> entities = majorRebateMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public MajorRebateEntity selectById(Long id) {
        return MajorRebateConverter.toMajorRebateEntity(majorRebateMapper.selectById(id));
    }


    @Override
    public List<MajorRebateEntity> selectByCondition(MajorRebateQueryParam param) {
        return MajorRebateConverter.toMajorRebateEntityList(majorRebateMapper.selectByCondition(param));
    }

    @Override
    public List<MajorRebateEntity> selectListByCate(Integer adminId, List<Integer> areaNos, Integer cate, String sku) {
        return majorRebateMapper.selectList(adminId, areaNos, cate, sku);
    }

}