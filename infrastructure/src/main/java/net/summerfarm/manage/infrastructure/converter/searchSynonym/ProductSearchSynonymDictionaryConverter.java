package net.summerfarm.manage.infrastructure.converter.searchSynonym;

import net.summerfarm.manage.infrastructure.model.searchSynonym.ProductSearchSynonymDictionary;
import net.summerfarm.manage.domain.searchSynonym.entity.ProductSearchSynonymDictionaryEntity;
import net.summerfarm.manage.domain.searchSynonym.param.command.ProductSearchSynonymDictionaryCommandParam;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;


/**
 *
 * <AUTHOR>
 * @date 2025-04-24 14:53:58
 * @version 1.0
 *
 */
public class ProductSearchSynonymDictionaryConverter {

    private ProductSearchSynonymDictionaryConverter() {
        // 无需实现
    }




    public static List<ProductSearchSynonymDictionaryEntity> toProductSearchSynonymDictionaryEntityList(List<ProductSearchSynonymDictionary> productSearchSynonymDictionaryList) {
        if (productSearchSynonymDictionaryList == null) {
            return Collections.emptyList();
        }
        List<ProductSearchSynonymDictionaryEntity> productSearchSynonymDictionaryEntityList = new ArrayList<>();
        for (ProductSearchSynonymDictionary productSearchSynonymDictionary : productSearchSynonymDictionaryList) {
            productSearchSynonymDictionaryEntityList.add(toProductSearchSynonymDictionaryEntity(productSearchSynonymDictionary));
        }
        return productSearchSynonymDictionaryEntityList;
}


    public static ProductSearchSynonymDictionaryEntity toProductSearchSynonymDictionaryEntity(ProductSearchSynonymDictionary productSearchSynonymDictionary) {
        if (productSearchSynonymDictionary == null) {
             return null;
        }
        ProductSearchSynonymDictionaryEntity productSearchSynonymDictionaryEntity = new ProductSearchSynonymDictionaryEntity();
        productSearchSynonymDictionaryEntity.setId(productSearchSynonymDictionary.getId());
        productSearchSynonymDictionaryEntity.setSynonymTerms(productSearchSynonymDictionary.getSynonymTerms());
        productSearchSynonymDictionaryEntity.setCreatedBy(productSearchSynonymDictionary.getCreatedBy());
        productSearchSynonymDictionaryEntity.setUpdatedBy(productSearchSynonymDictionary.getUpdatedBy());
        productSearchSynonymDictionaryEntity.setCreatedAt(productSearchSynonymDictionary.getCreatedAt());
        productSearchSynonymDictionaryEntity.setUpdatedAt(productSearchSynonymDictionary.getUpdatedAt());
        return productSearchSynonymDictionaryEntity;
    }








    public static ProductSearchSynonymDictionary toProductSearchSynonymDictionary(ProductSearchSynonymDictionaryCommandParam param) {
        if (param == null) {
            return null;
        }
        ProductSearchSynonymDictionary productSearchSynonymDictionary = new ProductSearchSynonymDictionary();
        productSearchSynonymDictionary.setId(param.getId());
        productSearchSynonymDictionary.setSynonymTerms(param.getSynonymTerms());
        productSearchSynonymDictionary.setCreatedBy(param.getCreatedBy());
        productSearchSynonymDictionary.setUpdatedBy(param.getUpdatedBy());
        productSearchSynonymDictionary.setCreatedAt(param.getCreatedAt());
        productSearchSynonymDictionary.setUpdatedAt(param.getUpdatedAt());
        return productSearchSynonymDictionary;
    }
}
