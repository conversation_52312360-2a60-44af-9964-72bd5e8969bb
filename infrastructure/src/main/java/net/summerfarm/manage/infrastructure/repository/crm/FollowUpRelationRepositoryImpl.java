package net.summerfarm.manage.infrastructure.repository.crm;

import net.summerfarm.manage.domain.crm.entity.FollowUpRelationEntity;
import net.summerfarm.manage.domain.crm.repository.FollowUpRelationRepository;
import net.summerfarm.manage.infrastructure.converter.crm.FollowUpRelationConverter;
import net.summerfarm.manage.infrastructure.mapper.crm.FollowUpRelationMapper;
import net.summerfarm.manage.infrastructure.model.crm.FollowUpRelation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2023-09-20 15:02:32
* @version 1.0
*
*/
@Repository
public class FollowUpRelationRepositoryImpl implements FollowUpRelationRepository {

    @Autowired
    private FollowUpRelationMapper followUpRelationMapper;


    @Override
    public FollowUpRelationEntity selectByPrimaryKey(Long id) {
        return FollowUpRelationConverter.toFollowUpRelationEntity(followUpRelationMapper.selectByPrimaryKey(id));
    }

    @Override
    public List<FollowUpRelationEntity> selectByCondition(FollowUpRelationEntity entity) {
        return FollowUpRelationConverter.toFollowUpRelationEntityList(followUpRelationMapper.selectByCondition(FollowUpRelationConverter.toFollowUpRelation(entity)));
    }

    @Override
    public FollowUpRelationEntity selectOne(FollowUpRelationEntity entity) {
        return FollowUpRelationConverter.toFollowUpRelationEntity(followUpRelationMapper.selectOne(FollowUpRelationConverter.toFollowUpRelation(entity)));
    }

    @Override
    public FollowUpRelationEntity insertSelective(FollowUpRelationEntity entity) {
        FollowUpRelation followUpRelation = FollowUpRelationConverter.toFollowUpRelation(entity);
        followUpRelationMapper.insertSelective(followUpRelation);
        return FollowUpRelationConverter.toFollowUpRelationEntity(followUpRelation);
    }

    @Override
    public int updateById(FollowUpRelationEntity entity){
        return followUpRelationMapper.updateById(FollowUpRelationConverter.toFollowUpRelation(entity));
    }


    @Override
    public int remove(Long id) {
        return followUpRelationMapper.remove(id);
    }

    @Override
    public List<FollowUpRelationEntity> batchQueryByMids(List<Long> mIds) {
        return FollowUpRelationConverter.toFollowUpRelationEntityList(followUpRelationMapper.batchQueryByMids(mIds));
    }
}