package net.summerfarm.manage.infrastructure.mapper.area;


import net.summerfarm.manage.domain.area.entity.Area;
import net.summerfarm.manage.domain.area.entity.AreaSimpleEntity;
import net.summerfarm.manage.domain.area.entity.LargeArea;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Mapper
public interface AreaMapper {

    List<Area> getNameByAreaNos(@Param("areaNos") Collection<Integer> areaNos);

    List<Area> listEntitiesByLargeAreaNos(@Param("largeAreaNos") Collection<Integer> largeAreaNos);

    List<AreaSimpleEntity> listByAreaNos(@Param("areaNos") Collection<Integer> areaNos);
    List<AreaSimpleEntity> listByAreaNames(@Param("names") Set<String> areaNames);

    List<AreaSimpleEntity> listByLargeAreaNos(@Param("largeAreaNos") Collection<Integer> largeAreaNos);

    /**
     * 查询所有有效的运营服务区
     *
     * @return 运营服务区
     */
    List<Area> selectAllMemberRulesValidArea();

    List<LargeArea> queryAllLargeArea(@Param("status") Integer status);
    List<LargeArea> queryLargeAreaByLargeAreaNos(@Param("nos") List<Integer> nos);

    Set<Integer> queryCloseAreaNos();

    List<AreaSimpleEntity> batchQueryLargeAreaInfoByLargeAreaNos(@Param("largeAreaNos") List<Integer> largeAreaNos);
}