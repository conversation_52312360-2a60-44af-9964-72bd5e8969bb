package net.summerfarm.manage.infrastructure.repository.order;


import net.summerfarm.manage.infrastructure.model.order.WxShippingInfoUploadRecord;
import net.summerfarm.manage.infrastructure.mapper.order.WxShippingInfoUploadRecordMapper;
import net.summerfarm.manage.infrastructure.converter.order.WxShippingInfoUploadRecordConverter;
import net.summerfarm.manage.domain.order.repository.WxShippingInfoUploadRecordQueryRepository;
import net.summerfarm.manage.domain.order.entity.WxShippingInfoUploadRecordEntity;
import net.summerfarm.manage.domain.order.param.query.WxShippingInfoUploadRecordQueryParam;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import java.util.List;




/**
*
* <AUTHOR>
* @date 2024-10-15 17:49:41
* @version 1.0
*
*/
@Repository
public class WxShippingInfoUploadRecordQueryRepositoryImpl implements WxShippingInfoUploadRecordQueryRepository {

    @Autowired
    private WxShippingInfoUploadRecordMapper wxShippingInfoUploadRecordMapper;


    @Override
    public PageInfo<WxShippingInfoUploadRecordEntity> getPage(WxShippingInfoUploadRecordQueryParam param) {
        Integer pageSize = param.getPageSize();
        Integer pageIndex = param.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<WxShippingInfoUploadRecordEntity> entities = wxShippingInfoUploadRecordMapper.getPage(param);
        return PageInfo.of(entities);
    }

    @Override
    public WxShippingInfoUploadRecordEntity selectById(Long id) {
        return WxShippingInfoUploadRecordConverter.toWxShippingInfoUploadRecordEntity(wxShippingInfoUploadRecordMapper.selectById(id));
    }


    @Override
    public List<WxShippingInfoUploadRecordEntity> selectByCondition(WxShippingInfoUploadRecordQueryParam param) {
        return WxShippingInfoUploadRecordConverter.toWxShippingInfoUploadRecordEntityList(wxShippingInfoUploadRecordMapper.selectByCondition(param));
    }

    @Override
    public WxShippingInfoUploadRecordEntity selectByMasterOrderNo(String masterOrderNo) {
        return wxShippingInfoUploadRecordMapper.selectByMasterOrderNo(masterOrderNo);
    }

}