package net.summerfarm.manage.infrastructure.converter.account;

import net.summerfarm.manage.infrastructure.model.account.ContactAdjust;
import net.summerfarm.manage.domain.account.entity.ContactAdjustEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 *
 * <AUTHOR>
 * @date 2023-10-26 16:20:20
 * @version 1.0
 *
 */
public class ContactAdjustConverter {

    private ContactAdjustConverter() {
        // 无需实现
    }




    public static List<ContactAdjustEntity> toContactAdjustEntityList(List<ContactAdjust> contactAdjustList) {
        if (contactAdjustList == null) {
            return Collections.emptyList();
        }
        List<ContactAdjustEntity> contactAdjustEntityList = new ArrayList<>();
        for (ContactAdjust contactAdjust : contactAdjustList) {
            contactAdjustEntityList.add(toContactAdjustEntity(contactAdjust));
        }
        return contactAdjustEntityList;
}


    public static ContactAdjustEntity toContactAdjustEntity(ContactAdjust contactAdjust) {
        if (contactAdjust == null) {
             return null;
        }
        ContactAdjustEntity contactAdjustEntity = new ContactAdjustEntity();
        contactAdjustEntity.setId(contactAdjust.getId());
        contactAdjustEntity.setAddTime(contactAdjust.getAddTime());
        contactAdjustEntity.setUpdateTime(contactAdjust.getUpdateTime());
        contactAdjustEntity.setMId(contactAdjust.getMId());
        contactAdjustEntity.setContactId(contactAdjust.getContactId());
        contactAdjustEntity.setNewPoi(contactAdjust.getNewPoi());
        contactAdjustEntity.setStatus(contactAdjust.getStatus());
        contactAdjustEntity.setNewProvince(contactAdjust.getNewProvince());
        contactAdjustEntity.setNewCity(contactAdjust.getNewCity());
        contactAdjustEntity.setNewArea(contactAdjust.getNewArea());
        contactAdjustEntity.setNewAddress(contactAdjust.getNewAddress());
        contactAdjustEntity.setNewHouseNumber(contactAdjust.getNewHouseNumber());
        return contactAdjustEntity;
    }






    public static List<ContactAdjust> toContactAdjustList(List<ContactAdjustEntity> contactAdjustEntityList) {
        if (contactAdjustEntityList == null) {
            return Collections.emptyList();
        }
        List<ContactAdjust> contactAdjustList = new ArrayList<>();
        for (ContactAdjustEntity contactAdjustEntity : contactAdjustEntityList) {
            contactAdjustList.add(toContactAdjust(contactAdjustEntity));
        }
        return contactAdjustList;
    }


    public static ContactAdjust toContactAdjust(ContactAdjustEntity contactAdjustEntity) {
        if (contactAdjustEntity == null) {
            return null;
        }
        ContactAdjust contactAdjust = new ContactAdjust();
        contactAdjust.setId(contactAdjustEntity.getId());
        contactAdjust.setAddTime(contactAdjustEntity.getAddTime());
        contactAdjust.setUpdateTime(contactAdjustEntity.getUpdateTime());
        contactAdjust.setMId(contactAdjustEntity.getMId());
        contactAdjust.setContactId(contactAdjustEntity.getContactId());
        contactAdjust.setNewPoi(contactAdjustEntity.getNewPoi());
        contactAdjust.setStatus(contactAdjustEntity.getStatus());
        contactAdjust.setNewProvince(contactAdjustEntity.getNewProvince());
        contactAdjust.setNewCity(contactAdjustEntity.getNewCity());
        contactAdjust.setNewArea(contactAdjustEntity.getNewArea());
        contactAdjust.setNewAddress(contactAdjustEntity.getNewAddress());
        contactAdjust.setNewHouseNumber(contactAdjustEntity.getNewHouseNumber());
        return contactAdjust;
    }
}
