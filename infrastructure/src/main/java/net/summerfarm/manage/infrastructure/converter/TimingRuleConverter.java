package net.summerfarm.manage.infrastructure.converter;

import net.summerfarm.manage.domain.timing.entity.TimingRuleEntity;
import net.summerfarm.manage.infrastructure.model.timing.TimingRule;

/**
 * 定期送规则转换器
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public class TimingRuleConverter {

    /**
     * 实体转模型
     */
    public static TimingRule entityToModel(TimingRuleEntity entity) {
        if (entity == null) {
            return null;
        }
        
        TimingRule model = new TimingRule();
        model.setId(entity.getId());
        model.setName(entity.getName());
        model.setTimingSku(entity.getTimingSku());
        model.setAreaNo(entity.getAreaNo());
        model.setDisplay(entity.getDisplay());
        model.setStartTime(entity.getStartTime());
        model.setEndTime(entity.getEndTime());
        model.setDeliveryStart(entity.getDeliveryStart());
        model.setDeliveryEnd(entity.getDeliveryEnd());
        model.setRuleInformation(entity.getRuleInformation());
        model.setDeliveryUnit(entity.getDeliveryUnit());
        model.setDeliveryUpperLimit(entity.getDeliveryUpperLimit());
        model.setUpdateTime(entity.getUpdateTime());
        model.setType(entity.getType());
        model.setPriority(entity.getPriority());
        model.setAutoCalculate(entity.getAutoCalculate());
        model.setDeliveryPeriod(entity.getDeliveryPeriod());
        model.setDeliveryStartType(entity.getDeliveryStartType());
        model.setCreateTime(entity.getCreateTime());
        model.setThreshold(entity.getThreshold());
        model.setPlusDay(entity.getPlusDay());
        
        return model;
    }

    /**
     * 模型转实体
     */
    public static TimingRuleEntity modelToEntity(TimingRule model) {
        if (model == null) {
            return null;
        }
        
        TimingRuleEntity entity = new TimingRuleEntity();
        entity.setId(model.getId());
        entity.setName(model.getName());
        entity.setTimingSku(model.getTimingSku());
        entity.setAreaNo(model.getAreaNo());
        entity.setDisplay(model.getDisplay());
        entity.setStartTime(model.getStartTime());
        entity.setEndTime(model.getEndTime());
        entity.setDeliveryStart(model.getDeliveryStart());
        entity.setDeliveryEnd(model.getDeliveryEnd());
        entity.setRuleInformation(model.getRuleInformation());
        entity.setDeliveryUnit(model.getDeliveryUnit());
        entity.setDeliveryUpperLimit(model.getDeliveryUpperLimit());
        entity.setUpdateTime(model.getUpdateTime());
        entity.setType(model.getType());
        entity.setPriority(model.getPriority());
        entity.setAutoCalculate(model.getAutoCalculate());
        entity.setDeliveryPeriod(model.getDeliveryPeriod());
        entity.setDeliveryStartType(model.getDeliveryStartType());
        entity.setCreateTime(model.getCreateTime());
        entity.setThreshold(model.getThreshold());
        entity.setPlusDay(model.getPlusDay());
        
        return entity;
    }
}
