package net.summerfarm.manage.infrastructure.mapper.activity;

import net.summerfarm.manage.domain.activity.valueObject.ActivitySkuDetailValueObject;
import net.summerfarm.manage.infrastructure.model.activity.ActivitySkuDetail;
import net.summerfarm.manage.domain.activity.param.query.ActivitySkuDetailQueryParam;
import net.summerfarm.manage.domain.activity.entity.ActivitySkuDetailEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-04-09 15:06:01
 * @version 1.0
 *
 */
@Mapper
public interface ActivitySkuDetailMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(ActivitySkuDetail record);

    /**
     * @Describe: 批量插入非空
     * @param records
     * @return
     */
    int batchInsertSelective(@Param("records") List<ActivitySkuDetail> records);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(ActivitySkuDetail record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    ActivitySkuDetail selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<ActivitySkuDetail> selectByCondition(ActivitySkuDetailQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<ActivitySkuDetailEntity> getPage(ActivitySkuDetailQueryParam param);

    List<ActivitySkuDetail> listByItemConfigs(@Param("list") List<Long> list, @Param("sku") String sku);
    List<ActivitySkuDetail> listByItemConfigsAndSkus(@Param("list")List<Long> list, @Param("skus") Collection<String> skus);

    List<ActivitySkuDetailValueObject> listDetailByItemConfigs(@Param("list") List<Long> list, @Param("sku") String sku);

    List<ActivitySkuDetailValueObject> selectByConditionV2(ActivitySkuDetailQueryParam param);

}

