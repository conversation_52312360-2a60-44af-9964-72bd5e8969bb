package net.summerfarm.manage.infrastructure.mapper.product;

import net.summerfarm.manage.infrastructure.model.product.ProductLabelValue;
import net.summerfarm.manage.domain.product.param.query.ProductLabelValueQueryParam;
import net.summerfarm.manage.domain.product.entity.ProductLabelValueEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-05-07 14:12:46
 * @version 1.0
 *
 */
@Mapper
public interface ProductLabelValueMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(ProductLabelValue record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(ProductLabelValue record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    ProductLabelValue selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<ProductLabelValue> selectByCondition(ProductLabelValueQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<ProductLabelValueEntity> getPage(ProductLabelValueQueryParam param);

    List<ProductLabelValueEntity> selectBySku(String sku);
}

