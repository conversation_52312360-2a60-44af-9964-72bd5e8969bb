package net.summerfarm.manage.infrastructure.mapper.admin;

import net.summerfarm.manage.infrastructure.model.admin.AdminDataPermission;
import net.summerfarm.manage.domain.admin.param.query.AdminDataPermissionQueryParam;
import net.summerfarm.manage.domain.admin.entity.AdminDataPermissionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-06-19 16:33:43
 * @version 1.0
 *
 */
@Mapper
public interface AdminDataPermissionMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(AdminDataPermission record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(AdminDataPermission record);

    /**
     * @Describe: 通过主键删除
     * @param
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    AdminDataPermission selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<AdminDataPermission> selectByCondition(AdminDataPermissionQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param param
     * @return
     */
    List<AdminDataPermissionEntity> getPage(AdminDataPermissionQueryParam param);
}

