package net.summerfarm.manage.infrastructure.converter.crm;

import net.summerfarm.manage.domain.crm.entity.FollowUpRelationEntity;
import net.summerfarm.manage.infrastructure.model.crm.FollowUpRelation;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-09-20 15:02:32
 * @version 1.0
 *
 */
public class FollowUpRelationConverter {


    private FollowUpRelationConverter() {
        // 无需实现
    }

    public static List<FollowUpRelationEntity> toFollowUpRelationEntityList(List<FollowUpRelation> followUpRelationList) {
        if (followUpRelationList == null) {
            return Collections.emptyList();
        }
        List<FollowUpRelationEntity> followUpRelationEntityList = new ArrayList<>();
        for (FollowUpRelation followUpRelation : followUpRelationList) {
            followUpRelationEntityList.add(toFollowUpRelationEntity(followUpRelation));
        }
        return followUpRelationEntityList;
    }

    public static FollowUpRelationEntity toFollowUpRelationEntity(FollowUpRelation followUpRelation) {
        if (followUpRelation == null) {
            return null;
        }
        FollowUpRelationEntity followUpRelationEntity = new FollowUpRelationEntity();
        followUpRelationEntity.setId(followUpRelation.getId());
        followUpRelationEntity.setMId(followUpRelation.getMId());
        followUpRelationEntity.setAdminId(followUpRelation.getAdminId());
        followUpRelationEntity.setAdminName(followUpRelation.getAdminName());
        followUpRelationEntity.setAddTime(followUpRelation.getAddTime());
        followUpRelationEntity.setReassign(followUpRelation.getReassign());
        followUpRelationEntity.setLastFollowUpTime(followUpRelation.getLastFollowUpTime());
        followUpRelationEntity.setReassignTime(followUpRelation.getReassignTime());
        followUpRelationEntity.setReason(followUpRelation.getReason());
        followUpRelationEntity.setFollowType(followUpRelation.getFollowType());
        followUpRelationEntity.setDangerDay(followUpRelation.getDangerDay());
        followUpRelationEntity.setTimingFollowType(followUpRelation.getTimingFollowType());
        followUpRelationEntity.setCareBdId(followUpRelation.getCareBdId());
        followUpRelationEntity.setMIds(followUpRelation.getMIds());
        return followUpRelationEntity;
    }

    public static List<FollowUpRelation> toFollowUpRelationList(List<FollowUpRelationEntity> followUpRelationEntityList) {
        if (followUpRelationEntityList == null) {
            return Collections.emptyList();
        }
        List<FollowUpRelation> followUpRelationList = new ArrayList<>();
        for (FollowUpRelationEntity followUpRelationEntity : followUpRelationEntityList) {
            followUpRelationList.add(toFollowUpRelation(followUpRelationEntity));
        }
        return followUpRelationList;
    }

    public static FollowUpRelation toFollowUpRelation(FollowUpRelationEntity followUpRelationEntity) {
        if (followUpRelationEntity == null) {
            return null;
        }
        FollowUpRelation followUpRelation = new FollowUpRelation();
        followUpRelation.setId(followUpRelationEntity.getId());
        followUpRelation.setMId(followUpRelationEntity.getMId());
        followUpRelation.setAdminId(followUpRelationEntity.getAdminId());
        followUpRelation.setAdminName(followUpRelationEntity.getAdminName());
        followUpRelation.setAddTime(followUpRelationEntity.getAddTime());
        followUpRelation.setReassign(followUpRelationEntity.getReassign());
        followUpRelation.setLastFollowUpTime(followUpRelationEntity.getLastFollowUpTime());
        followUpRelation.setReassignTime(followUpRelationEntity.getReassignTime());
        followUpRelation.setReason(followUpRelationEntity.getReason());
        followUpRelation.setFollowType(followUpRelationEntity.getFollowType());
        followUpRelation.setDangerDay(followUpRelationEntity.getDangerDay());
        followUpRelation.setTimingFollowType(followUpRelationEntity.getTimingFollowType());
        followUpRelation.setCareBdId(followUpRelationEntity.getCareBdId());
        followUpRelation.setMIds(followUpRelationEntity.getMIds());

        return followUpRelation;
    }
}
