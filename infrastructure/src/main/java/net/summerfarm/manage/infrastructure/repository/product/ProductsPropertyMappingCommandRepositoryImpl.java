package net.summerfarm.manage.infrastructure.repository.product;

import net.summerfarm.manage.infrastructure.model.product.ProductsPropertyMapping;
import net.summerfarm.manage.infrastructure.mapper.product.ProductsPropertyMappingMapper;
import net.summerfarm.manage.infrastructure.converter.product.ProductsPropertyMappingConverter;
import net.summerfarm.manage.domain.product.repository.ProductsPropertyMappingCommandRepository;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyMappingEntity;
import net.summerfarm.manage.domain.product.param.command.ProductsPropertyMappingCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2024-05-07 16:18:00
* @version 1.0
*
*/
@Repository
public class ProductsPropertyMappingCommandRepositoryImpl implements ProductsPropertyMappingCommandRepository {

    @Autowired
    private ProductsPropertyMappingMapper productsPropertyMappingMapper;
    @Override
    public ProductsPropertyMappingEntity insertSelective(ProductsPropertyMappingCommandParam param) {
        ProductsPropertyMapping productsPropertyMapping = ProductsPropertyMappingConverter.toProductsPropertyMapping(param);
        productsPropertyMappingMapper.insertSelective(productsPropertyMapping);
        return ProductsPropertyMappingConverter.toProductsPropertyMappingEntity(productsPropertyMapping);
    }

    @Override
    public int updateSelectiveById(ProductsPropertyMappingCommandParam param){
        return productsPropertyMappingMapper.updateSelectiveById(ProductsPropertyMappingConverter.toProductsPropertyMapping(param));
    }


    @Override
    public int remove(Long id) {
        return productsPropertyMappingMapper.remove(id);
    }

    @Override
    public void deleteBySelective(int type, Integer propertyId, List<Integer> pdIdList) {
        productsPropertyMappingMapper.deleteBySelective(type, propertyId, pdIdList);
    }
}