package net.summerfarm.manage.infrastructure.mapper.order;

import net.summerfarm.manage.domain.order.flatObject.AfterSaleOrderFlatObject;
import net.summerfarm.manage.infrastructure.model.order.AfterSaleOrder;
import net.summerfarm.manage.domain.order.param.query.AfterSaleOrderQueryParam;
import net.summerfarm.manage.domain.order.entity.AfterSaleOrderEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-01-18 16:06:21
 * @version 1.0
 *
 */
@Mapper
public interface AfterSaleOrderMapper{
    /**
     * @Describe: 插入非空
     * @param record
     * @return
     */
    int insertSelective(AfterSaleOrder record);

    /**
     * @Describe: 通过主键修改非空的数据
     * @param record
     * @return
     */
    int updateSelectiveById(AfterSaleOrder record);

    /**
     * @Describe: 通过主键删除
     * @param record
     * @return
     */
    int remove(@Param("id") Long id);

    /**
     * @Describe: 通过主键查询唯一一条数据
     * @param id
     * @return
     */
    AfterSaleOrder selectById(@Param("id") Long id);


    /**
     * @Describe: 通过指定条件查询数据列表
     * @param record
     * @return
     */
    List<AfterSaleOrder> selectByCondition(AfterSaleOrderQueryParam param);

    /**
     * @Describe: 该分页接口仅为搭建的模板骨架，具体的业务逻辑需要使用方自行调整
     * @param input
     * @return
     */
    List<AfterSaleOrderEntity> getPage(AfterSaleOrderQueryParam param);

    /**
     * @Describe: 通过指定条件查询数据列表
     * @param param
     * @return
     */
    List<AfterSaleOrderFlatObject> selectByOrderNo(AfterSaleOrderQueryParam param);

    /**
     * @Describe: 通过订单号查询当前订单号的售后信息
     * @param normalOrderNos
     * @return
     */
    List<AfterSaleOrder> getAfterSaleInfoByOrderNos(@Param("normalOrderNos") List<String> normalOrderNos);

    List<AfterSaleOrderEntity> getNotDeliveryAfterSaleInfoByOrderNo(@Param("orderNo") String orderNo);
}

