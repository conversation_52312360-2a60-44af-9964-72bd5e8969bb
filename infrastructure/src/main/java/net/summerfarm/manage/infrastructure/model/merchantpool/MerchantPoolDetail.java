package net.summerfarm.manage.infrastructure.model.merchantpool;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class MerchantPoolDetail implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 人群包id
     */
    private Long poolInfoId;

    /**
     * 商户id
     */
    private Long mId;

    /**
     * 店铺类型
     */
    private String size;

    /**
     * 运营服务区域
     */
    private Integer areaNo;

    /**
     * 版本(只会存在两个版本)
     */
    private Integer version;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}