<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.delivery.DeliveryPlanMapper">
    <!-- 结果集映射 -->
    <resultMap id="deliveryPlanResultMap" type="net.summerfarm.manage.infrastructure.model.delivery.DeliveryPlan">
		<id column="id" property="id" jdbcType="INTEGER"/>
		<result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
		<result column="status" property="status" jdbcType="SMALLINT"/>
		<result column="delivery_time" property="deliveryTime" jdbcType="DATE"/>
		<result column="quantity" property="quantity" jdbcType="INTEGER"/>
		<result column="master_order_no" property="masterOrderNo" jdbcType="VARCHAR"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="contact_id" property="contactId" jdbcType="INTEGER"/>
		<result column="deliverytype" property="deliverytype" jdbcType="INTEGER"/>
		<result column="time_frame" property="timeFrame" jdbcType="VARCHAR"/>
		<result column="account_id" property="accountId" jdbcType="NUMERIC"/>
		<result column="admin_id" property="adminId" jdbcType="INTEGER"/>
		<result column="order_store_no" property="orderStoreNo" jdbcType="INTEGER"/>
		<result column="put_off_time" property="putOffTime" jdbcType="DATE"/>
		<result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
		<result column="old_delivery_time" property="oldDeliveryTime" jdbcType="DATE"/>
		<result column="intercept_flag" property="interceptFlag" jdbcType="INTEGER"/>
		<result column="intercept_time" property="interceptTime" jdbcType="TIMESTAMP"/>
		<result column="show_flag" property="showFlag" jdbcType="TINYINT"/>
		<result column="delivery_evaluation_status" property="deliveryEvaluationStatus" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="deliveryPlanResultEntityMap" type="net.summerfarm.manage.domain.delivery.entity.DeliveryPlanEntity">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="SMALLINT"/>
        <result column="delivery_time" property="deliveryTime" jdbcType="DATE"/>
        <result column="quantity" property="quantity" jdbcType="INTEGER"/>
        <result column="master_order_no" property="masterOrderNo" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="contact_id" property="contactId" jdbcType="INTEGER"/>
        <result column="deliverytype" property="deliverytype" jdbcType="INTEGER"/>
        <result column="time_frame" property="timeFrame" jdbcType="VARCHAR"/>
        <result column="account_id" property="accountId" jdbcType="NUMERIC"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="order_store_no" property="orderStoreNo" jdbcType="INTEGER"/>
        <result column="put_off_time" property="putOffTime" jdbcType="DATE"/>
        <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
        <result column="old_delivery_time" property="oldDeliveryTime" jdbcType="DATE"/>
        <result column="intercept_flag" property="interceptFlag" jdbcType="INTEGER"/>
        <result column="intercept_time" property="interceptTime" jdbcType="TIMESTAMP"/>
        <result column="show_flag" property="showFlag" jdbcType="TINYINT"/>
        <result column="delivery_evaluation_status" property="deliveryEvaluationStatus" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="OrderDeliveryPlanFlatObject" type="net.summerfarm.manage.domain.order.flatObject.OrderDeliveryPlanFlatObject">
        <id column="order_id" property="orderId" jdbcType="BIGINT"/>
        <id column="delivery_time" property="deliveryTime" jdbcType="TIMESTAMP"/>
        <id column="contact_id" property="contactId" jdbcType="INTEGER"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="mname" property="mname" jdbcType="VARCHAR"/>
        <result column="size" property="msize" jdbcType="VARCHAR"/>
        <result column="m_id" property="mId" jdbcType="BIGINT"/>
        <result column="contact" property="contactName" jdbcType="VARCHAR"/>
        <result column="phone" property="contactPhone" jdbcType="VARCHAR"/>
        <result column="contactAddress" property="contactAddress" jdbcType="VARCHAR"/>
        <result column="total_price" property="totalPrice" jdbcType="DECIMAL"/>
        <result column="delivery_fee" property="deliveryFee" jdbcType="DECIMAL"/>
        <result column="out_times_fee" property="outTimesFee" jdbcType="DECIMAL"/>
        <result column="remark" property="orderRemark" jdbcType="VARCHAR"/>
        <result column="time_frame" property="timeFrame" jdbcType="VARCHAR"/>
        <result column="order_time" property="orderTime" jdbcType="TIMESTAMP"/>
        <result column="order_store_no" property="storeNo"/>
        <result column="dp_id" property="dpId"/>
        <result column="name_remakes" property="brandName" jdbcType="VARCHAR"/>
        <result column="admin_id" property="bigCustomerId"/>
        <collection property="orderDeliveryPlanItemFlatObjectList" ofType="net.summerfarm.manage.domain.order.flatObject.OrderDeliveryPlanItemFlatObject">
            <id column="id" property="id" jdbcType="BIGINT"/>
            <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
            <result column="amount" property="amount" jdbcType="VARCHAR"/>
            <result column="original_price" property="originalPrice" jdbcType="DECIMAL"/>
            <result column="price" property="price" jdbcType="DECIMAL"/>
            <result column="weight" property="weight" jdbcType="VARCHAR"/>
            <result column="maturity" property="maturity" jdbcType="VARCHAR"/>
            <result column="sku" property="sku" jdbcType="VARCHAR"/>
            <result column="volume" property="volume" jdbcType="VARCHAR"/>
            <result column="storage_location" property="storageLocation" jdbcType="TINYINT"/>
            <result column="oi_order_no" property="orderNo" jdbcType="VARCHAR"/>
            <result column="product_type" property="productType"/>
            <result column="weight_num" property="weightNum" jdbcType="DECIMAL"/>
        </collection>
    </resultMap>

    <!-- 列定义 -->
    <sql id="deliveryPlanColumns">
          t.id,
          t.order_no,
          t.status,
          t.delivery_time,
          t.quantity,
          t.master_order_no,
          t.update_time,
          t.contact_id,
          t.deliverytype,
          t.time_frame,
          t.account_id,
          t.admin_id,
          t.order_store_no,
          t.put_off_time,
          t.add_time,
          t.old_delivery_time,
          t.intercept_flag,
          t.intercept_time,
          t.show_flag,
          t.delivery_evaluation_status
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="orderNo != null and orderNo !=''">
                AND t.order_no = #{orderNo}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="deliveryTime != null">
                AND t.delivery_time = #{deliveryTime}
            </if>
			<if test="quantity != null">
                AND t.quantity = #{quantity}
            </if>
			<if test="masterOrderNo != null and masterOrderNo !=''">
                AND t.master_order_no = #{masterOrderNo}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="contactId != null">
                AND t.contact_id = #{contactId}
            </if>
			<if test="deliverytype != null">
                AND t.deliverytype = #{deliverytype}
            </if>
			<if test="timeFrame != null and timeFrame !=''">
                AND t.time_frame = #{timeFrame}
            </if>
			<if test="accountId != null">
                AND t.account_id = #{accountId}
            </if>
			<if test="adminId != null">
                AND t.admin_id = #{adminId}
            </if>
			<if test="orderStoreNo != null">
                AND t.order_store_no = #{orderStoreNo}
            </if>
			<if test="putOffTime != null">
                AND t.put_off_time = #{putOffTime}
            </if>
			<if test="addTime != null">
                AND t.add_time = #{addTime}
            </if>
			<if test="ordDeliveryTime != null">
                AND t.ord_delivery_time = #{ordDeliveryTime}
            </if>
			<if test="oldDeliveryTime != null">
                AND t.old_delivery_time = #{oldDeliveryTime}
            </if>
			<if test="interceptFlag != null">
                AND t.intercept_flag = #{interceptFlag}
            </if>
			<if test="interceptTime != null">
                AND t.intercept_time = #{interceptTime}
            </if>
			<if test="showFlag != null">
                AND t.show_flag = #{showFlag}
            </if>
			<if test="deliveryEvaluationStatus != null">
                AND t.delivery_evaluation_status = #{deliveryEvaluationStatus}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="orderNo != null">
                    t.order_no = #{orderNo},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="deliveryTime != null">
                    t.delivery_time = #{deliveryTime},
                </if>
                <if test="quantity != null">
                    t.quantity = #{quantity},
                </if>
                <if test="masterOrderNo != null">
                    t.master_order_no = #{masterOrderNo},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="contactId != null">
                    t.contact_id = #{contactId},
                </if>
                <if test="deliverytype != null">
                    t.deliverytype = #{deliverytype},
                </if>
                <if test="timeFrame != null">
                    t.time_frame = #{timeFrame},
                </if>
                <if test="accountId != null">
                    t.account_id = #{accountId},
                </if>
                <if test="adminId != null">
                    t.admin_id = #{adminId},
                </if>
                <if test="orderStoreNo != null">
                    t.order_store_no = #{orderStoreNo},
                </if>
                <if test="putOffTime != null">
                    t.put_off_time = #{putOffTime},
                </if>
                <if test="addTime != null">
                    t.add_time = #{addTime},
                </if>
                <if test="ordDeliveryTime != null">
                    t.ord_delivery_time = #{ordDeliveryTime},
                </if>
                <if test="oldDeliveryTime != null">
                    t.old_delivery_time = #{oldDeliveryTime},
                </if>
                <if test="interceptFlag != null">
                    t.intercept_flag = #{interceptFlag},
                </if>
                <if test="interceptTime != null">
                    t.intercept_time = #{interceptTime},
                </if>
                <if test="showFlag != null">
                    t.show_flag = #{showFlag},
                </if>
                <if test="deliveryEvaluationStatus != null">
                    t.delivery_evaluation_status = #{deliveryEvaluationStatus},
                </if>
        </trim>
    </sql>

    <update id="updateInfoById" parameterType="net.summerfarm.manage.infrastructure.model.delivery.DeliveryPlan">
        UPDATE delivery_plan t
        <include refid="whereColumnByUpdate"></include>
        <where>
            t.id = #{id,jdbcType=INTEGER}
        </where>
    </update>
    <select id="getAutoConfirmOrder"
            resultType="net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject">
        SELECT o.order_no orderNo,o.m_id mId,o.type,dp.quantity,dp.id,dp.deliverytype,dp.intercept_flag interceptFlag,dp.order_store_no orderStoreNo,
               dp.delivery_time deliveryTime,dp.contact_id contactId
        from delivery_plan dp
                 LEFT JOIN orders o on o.order_no = dp.order_no
        where dp.delivery_time <![CDATA[<=]]> #{dateTime}
          and o.status = 3
          AND dp.status = 3
          AND o.order_time > DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
        <if test="orderNo != null">
            and o.order_no = #{orderNo}
        </if>
        ORDER BY o.order_id asc
        limit #{pageStart}, #{pageSize}
    </select>

    <select id="getDeliveryPlanQuantity" resultType="java.lang.Integer">
        select IFNULL(sum(quantity), 0) quantity
        from delivery_plan
        where status = 6
          and order_no = #{orderNo}
    </select>
    <select id="noticeLists"
            resultType="net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject">
        SELECT msa.openid, concat(c.province,c.city,c.area,c.address) contact, c.phone, dp.quantity,
               dp.delivery_time deliveryTime, o.order_no orderNo, dp.order_store_no orderStoreNo
        FROM delivery_plan dp
                 INNER JOIN orders o ON dp.order_no = o.order_no
            AND dp.delivery_time = #{deliveryTime}
            AND dp.status IN (2,3,6)
            AND o.status IN (2,3,6)
            AND o.type != 3
            <if test="orderNo != null">
                AND o.order_no = #{orderNo}
            </if>
        INNER JOIN contact c ON dp.contact_id = c.contact_id
            INNER JOIN merchant_sub_account msa ON o.m_id = msa.m_id and msa.delete_flag = 1 and msa.status = 1
            INNER JOIN merchant m on m.m_id = o.m_id and m.area_no not in (2836,2250,4722,7524,11031,15655,16536,6001,13351)
        limit #{pageStart}, #{pageSize}
    </select>
    <select id="timingNoticeLists"
            resultType="net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject">
        SELECT msa.openid, c.contact, c.phone, oi.pd_name pdName, dp.quantity, dp.delivery_time deliveryTime, o.order_no orderNo
        FROM delivery_plan dp
                 INNER JOIN orders o ON dp.order_no = o.order_no
            AND o.type = 1
            AND dp.delivery_time = #{deliveryTime}
            AND dp.status IN (2,3,6)
            AND o.status IN (2,3,6)
                 INNER JOIN order_item oi ON o.order_no = oi.order_no
                 INNER JOIN contact c ON dp.contact_id = c.contact_id
                 INNER JOIN merchant_sub_account msa ON o.m_id = msa.m_id
        where msa.status = 1 and msa.delete_flag = 1
        <if test="orderNo != null">
            AND o.order_no = #{orderNo}
        </if>
        limit #{pageStart}, #{pageSize}
    </select>

    <select id="queryTimingOrderNoFreezeProxySaleNoWarehouse" resultType="net.summerfarm.manage.domain.delivery.flatObject.NoFreezeProxySaleNoWareNoSkuFlatObject">
        select dp.order_store_no as storeNo,
               oi.sku            as sku,
               ifnull(sum(dp.quantity), 0) as quantity
        from delivery_plan dp
        inner JOIN orders o on dp.order_no = o.order_no
        inner join merchant m on m.m_id = o.m_id
        inner join order_item oi on o.order_no = oi.order_no
        inner join inventory i on i.sub_type = 1 and i.sku = oi.sku
        WHERE dp.delivery_time &gt;= #{startDate}
        and dp.delivery_time &lt;= #{endDate}
        and dp.order_store_no = #{storeNo}
        and dp.status = 2
        and o.type=1
        group by dp.order_store_no,oi.sku
    </select>
    <select id="getWaitingDeliveryPlanQuantity" resultType="net.summerfarm.manage.domain.delivery.flatObject.DeliveryPlanFlatObject">
        SELECT quantity, order_no orderNo, status
        FROM delivery_plan
        WHERE status in (2, 3, 6)
        AND order_no in
        <foreach collection="normalOrderNos" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryValidOrderDeliveryPlanDetail" resultMap ="OrderDeliveryPlanFlatObject">
        SELECT o.order_id,
               o.order_no,
               m.mname,
               m.size,
               m.m_id,
               con.contact,
               dp.delivery_time,
               dp.deliverytype,
               con.phone,
               CONCAT(con.province, con.city, con.area, con.address, ifnull(con.house_number, '')) contactAddress,
               o.total_price,
               o.delivery_fee,
               o.out_times_fee,
               o.remark,
               oi.id,
               oi.pd_name,
               if(o.type = 1, dp.quantity, oi.amount) amount,
               oi.original_price,
               oi.price,
               oi.weight,
               oi.maturity,
               oi.sku,
               oi.volume,
               oi.storage_location,
               con.contact_id,
               dp.time_frame,
               o.order_time,
               oi.order_no oi_order_no,
               oi.product_type,
               oi.weight_num,
               dp.order_store_no,
               dp.id dp_id,
               a.name_remakes,
               a.admin_id
        FROM (SELECT *
              FROM delivery_plan
              WHERE
                status in (3, 6)
                AND show_flag = 0
                <if test="deliveryTime != null">
                    AND delivery_time = #{deliveryTime}
                </if>
                <if test="orderNoList != null and orderNoList.size != 0">
                    AND order_no in
                    <foreach collection="orderNoList" separator="," open="(" close=")" item="orderNo">
                        #{orderNo}
                    </foreach>
                </if>
            ) dp
                 INNER JOIN orders o on dp.order_no = o.order_no
            AND o.status in (3, 6) AND o.type in (0, 1, 3, 12, 30)
                 INNER JOIN order_item oi ON oi.order_no = o.order_no AND oi.category_id != 3 AND oi.status in (3, 6)
         LEFT JOIN merchant m ON o.m_id = m.m_id
            LEFT JOIN contact con on dp.contact_id = con.contact_id
         LEFT JOIN admin a on m.admin_id=a.admin_id
    </select>


    <select id="getDeliveryPlanByOrderNo" resultMap="deliveryPlanResultEntityMap">
        /*FORCE_MASTER*/
        select
        <include refid="deliveryPlanColumns" />
        from delivery_plan t
        where t.status = 6
        and t.order_no = #{orderNo}
    </select>
    <select id="getDeliveryPlanQuantityById" resultType="java.lang.Integer">
        select IFNULL(sum(quantity), 0) quantity
        from delivery_plan
        where status = 3
        and id = #{id}
    </select>

</mapper>