<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.permission.AdminDataPermissionMapper">

    <resultMap id="BaseResultMap" type="net.summerfarm.manage.infrastructure.model.permission.AdminDataPermission">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="admin_id" property="adminId" jdbcType="INTEGER"/>
        <result column="permission_value" property="permissionValue" jdbcType="VARCHAR"/>
        <result column="permission_name" property="permissionName" jdbcType="VARCHAR"/>
        <result column="addtime" property="addtime" jdbcType="TIMESTAMP"/>
        <result column="type" property="type"/>
        <result column="warehouse_no" property="warehouseNo"/>
    </resultMap>



    <select id="selectByAdminId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select id, admin_id, permission_name, permission_value, addtime
        FROM admin_data_permission
        WHERE admin_id = #{adminId}
    </select>

</mapper>