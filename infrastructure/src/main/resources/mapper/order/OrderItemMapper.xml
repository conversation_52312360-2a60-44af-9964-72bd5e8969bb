<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.order.OrderItemMapper">
    <!-- 结果集映射 -->
    <resultMap id="orderItemResultMap" type="net.summerfarm.manage.infrastructure.model.order.OrderItem">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
		<result column="sku" property="sku" jdbcType="VARCHAR"/>
		<result column="weight" property="weight" jdbcType="VARCHAR"/>
		<result column="maturity" property="maturity" jdbcType="VARCHAR"/>
		<result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
		<result column="category_id" property="categoryId" jdbcType="INTEGER"/>
		<result column="amount" property="amount" jdbcType="INTEGER"/>
		<result column="price" property="price" jdbcType="DOUBLE"/>
		<result column="original_price" property="originalPrice" jdbcType="DOUBLE"/>
		<result column="picture_path" property="picturePath" jdbcType="VARCHAR"/>
		<result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
		<result column="storage_location" property="storageLocation" jdbcType="TINYINT"/>
		<result column="suit_id" property="suitId" jdbcType="INTEGER"/>
		<result column="suit_name" property="suitName" jdbcType="VARCHAR"/>
		<result column="suit_amount" property="suitAmount" jdbcType="INTEGER"/>
		<result column="status" property="status" jdbcType="INTEGER"/>
		<result column="rebate_type" property="rebateType" jdbcType="INTEGER"/>
		<result column="rebate_number" property="rebateNumber" jdbcType="DOUBLE"/>
		<result column="m_price" property="mPrice" jdbcType="DOUBLE"/>
		<result column="volume" property="volume" jdbcType="VARCHAR"/>
		<result column="weight_num" property="weightNum" jdbcType="DOUBLE"/>
		<result column="use_coupon" property="useCoupon" jdbcType="TINYINT"/>
		<result column="max_threshold" property="maxThreshold" jdbcType="INTEGER"/>
		<result column="total_price" property="totalPrice" jdbcType="DOUBLE"/>
		<result column="product_type" property="productType" jdbcType="INTEGER"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="actual_total_price" property="actualTotalPrice" jdbcType="DOUBLE"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
		<result column="info" property="info" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="orderItemEntityResultMap" type="net.summerfarm.manage.domain.order.entity.OrderItemEntity">
        <id column="id" property="id" jdbcType="NUMERIC"/>
        <result column="pd_name" property="pdName" jdbcType="VARCHAR"/>
        <result column="sku" property="sku" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="maturity" property="maturity" jdbcType="VARCHAR"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="category_id" property="categoryId" jdbcType="INTEGER"/>
        <result column="amount" property="amount" jdbcType="INTEGER"/>
        <result column="price" property="price" jdbcType="DOUBLE"/>
        <result column="original_price" property="originalPrice" jdbcType="DOUBLE"/>
        <result column="picture_path" property="picturePath" jdbcType="VARCHAR"/>
        <result column="add_time" property="addTime" jdbcType="TIMESTAMP"/>
        <result column="storage_location" property="storageLocation" jdbcType="TINYINT"/>
        <result column="suit_id" property="suitId" jdbcType="INTEGER"/>
        <result column="suit_name" property="suitName" jdbcType="VARCHAR"/>
        <result column="suit_amount" property="suitAmount" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="rebate_type" property="rebateType" jdbcType="INTEGER"/>
        <result column="rebate_number" property="rebateNumber" jdbcType="DOUBLE"/>
        <result column="m_price" property="mPrice" jdbcType="DOUBLE"/>
        <result column="volume" property="volume" jdbcType="VARCHAR"/>
        <result column="weight_num" property="weightNum" jdbcType="DOUBLE"/>
        <result column="use_coupon" property="useCoupon" jdbcType="TINYINT"/>
        <result column="max_threshold" property="maxThreshold" jdbcType="INTEGER"/>
        <result column="total_price" property="totalPrice" jdbcType="DOUBLE"/>
        <result column="product_type" property="productType" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="actual_total_price" property="actualTotalPrice" jdbcType="DOUBLE"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="info" property="info" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="orderItemColumns">
          t.id,
          t.pd_name,
          t.sku,
          t.weight,
          t.maturity,
          t.order_no,
          t.category_id,
          t.amount,
          t.price,
          t.original_price,
          t.picture_path,
          t.add_time,
          t.storage_location,
          t.suit_id,
          t.suit_name,
          t.suit_amount,
          t.status,
          t.rebate_type,
          t.rebate_number,
          t.m_price,
          t.volume,
          t.weight_num,
          t.use_coupon,
          t.max_threshold,
          t.total_price,
          t.product_type,
          t.create_time,
          t.actual_total_price,
          t.update_time,
          t.sku_name,
          t.info
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="pdName != null and pdName !=''">
                AND t.pd_name = #{pdName}
            </if>
			<if test="sku != null and sku !=''">
                AND t.sku = #{sku}
            </if>
			<if test="weight != null and weight !=''">
                AND t.weight = #{weight}
            </if>
			<if test="maturity != null and maturity !=''">
                AND t.maturity = #{maturity}
            </if>
			<if test="orderNo != null and orderNo !=''">
                AND t.order_no = #{orderNo}
            </if>
			<if test="categoryId != null">
                AND t.category_id = #{categoryId}
            </if>
			<if test="amount != null">
                AND t.amount = #{amount}
            </if>
			<if test="price != null">
                AND t.price = #{price}
            </if>
			<if test="originalPrice != null">
                AND t.original_price = #{originalPrice}
            </if>
			<if test="picturePath != null and picturePath !=''">
                AND t.picture_path = #{picturePath}
            </if>
			<if test="addTime != null">
                AND t.add_time = #{addTime}
            </if>
			<if test="storageLocation != null">
                AND t.storage_location = #{storageLocation}
            </if>
			<if test="suitId != null">
                AND t.suit_id = #{suitId}
            </if>
			<if test="suitName != null and suitName !=''">
                AND t.suit_name = #{suitName}
            </if>
			<if test="suitAmount != null">
                AND t.suit_amount = #{suitAmount}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="rebateType != null">
                AND t.rebate_type = #{rebateType}
            </if>
			<if test="rebateNumber != null">
                AND t.rebate_number = #{rebateNumber}
            </if>
			<if test="mPrice != null">
                AND t.m_price = #{mPrice}
            </if>
			<if test="volume != null and volume !=''">
                AND t.volume = #{volume}
            </if>
			<if test="weightNum != null">
                AND t.weight_num = #{weightNum}
            </if>
			<if test="useCoupon != null">
                AND t.use_coupon = #{useCoupon}
            </if>
			<if test="maxThreshold != null">
                AND t.max_threshold = #{maxThreshold}
            </if>
			<if test="totalPrice != null">
                AND t.total_price = #{totalPrice}
            </if>
			<if test="productType != null">
                AND t.product_type = #{productType}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="actualTotalPrice != null">
                AND t.actual_total_price = #{actualTotalPrice}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="skuName != null and skuName !=''">
                AND t.sku_name = #{skuName}
            </if>
			<if test="info != null and info !=''">
                AND t.info = #{info}
            </if>
            <if test="orderNos != null and orderNos.size > 0">
                AND t.order_no in
                <foreach collection="orderNos" item="orderNo" close=")" open="(" separator=",">
                    #{orderNo}
                </foreach>
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="pdName != null">
                    t.pd_name = #{pdName},
                </if>
                <if test="sku != null">
                    t.sku = #{sku},
                </if>
                <if test="weight != null">
                    t.weight = #{weight},
                </if>
                <if test="maturity != null">
                    t.maturity = #{maturity},
                </if>
                <if test="orderNo != null">
                    t.order_no = #{orderNo},
                </if>
                <if test="categoryId != null">
                    t.category_id = #{categoryId},
                </if>
                <if test="amount != null">
                    t.amount = #{amount},
                </if>
                <if test="price != null">
                    t.price = #{price},
                </if>
                <if test="originalPrice != null">
                    t.original_price = #{originalPrice},
                </if>
                <if test="picturePath != null">
                    t.picture_path = #{picturePath},
                </if>
                <if test="addTime != null">
                    t.add_time = #{addTime},
                </if>
                <if test="storageLocation != null">
                    t.storage_location = #{storageLocation},
                </if>
                <if test="suitId != null">
                    t.suit_id = #{suitId},
                </if>
                <if test="suitName != null">
                    t.suit_name = #{suitName},
                </if>
                <if test="suitAmount != null">
                    t.suit_amount = #{suitAmount},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="rebateType != null">
                    t.rebate_type = #{rebateType},
                </if>
                <if test="rebateNumber != null">
                    t.rebate_number = #{rebateNumber},
                </if>
                <if test="mPrice != null">
                    t.m_price = #{mPrice},
                </if>
                <if test="volume != null">
                    t.volume = #{volume},
                </if>
                <if test="weightNum != null">
                    t.weight_num = #{weightNum},
                </if>
                <if test="useCoupon != null">
                    t.use_coupon = #{useCoupon},
                </if>
                <if test="maxThreshold != null">
                    t.max_threshold = #{maxThreshold},
                </if>
                <if test="totalPrice != null">
                    t.total_price = #{totalPrice},
                </if>
                <if test="productType != null">
                    t.product_type = #{productType},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="actualTotalPrice != null">
                    t.actual_total_price = #{actualTotalPrice},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="skuName != null">
                    t.sku_name = #{skuName},
                </if>
                <if test="info != null">
                    t.info = #{info},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="orderItemResultMap" >
        SELECT <include refid="orderItemColumns" />
        FROM order_item t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.order.param.query.OrderItemQueryParam"  resultType="net.summerfarm.manage.domain.order.entity.OrderItemEntity" >
        SELECT
            t.id id,
            t.pd_name pdName,
            t.sku sku,
            t.weight weight,
            t.maturity maturity,
            t.order_no orderNo,
            t.category_id categoryId,
            t.amount amount,
            t.price price,
            t.original_price originalPrice,
            t.picture_path picturePath,
            t.add_time addTime,
            t.storage_location storageLocation,
            t.suit_id suitId,
            t.suit_name suitName,
            t.suit_amount suitAmount,
            t.status status,
            t.rebate_type rebateType,
            t.rebate_number rebateNumber,
            t.m_price mPrice,
            t.volume volume,
            t.weight_num weightNum,
            t.use_coupon useCoupon,
            t.max_threshold maxThreshold,
            t.total_price totalPrice,
            t.product_type productType,
            t.create_time createTime,
            t.actual_total_price actualTotalPrice,
            t.update_time updateTime,
            t.sku_name skuName,
            t.info info
        FROM order_item t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.order.param.query.OrderItemQueryParam" resultMap="orderItemResultMap" >
        SELECT <include refid="orderItemColumns" />
        FROM order_item t
        <include refid="whereColumnBySelect"></include>
    </select>
    <select id="selectTimingOrderQuantity" parameterType="java.lang.String" resultType="java.lang.Integer">
        select IFNULL(SUM(oi.amount), 0) quantity
        FROM order_item oi
        WHERE oi.category_id <![CDATA[<>]]> 3
          AND oi.order_no = #{orderNo}
    </select>
    <select id="selectByOrderNo" resultMap="orderItemResultMap">
        SELECT <include refid="orderItemColumns" />
        FROM order_item t
        WHERE t.order_no = #{orderNo}
    </select>


    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.order.OrderItem" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO order_item
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="pdName != null">
				  pd_name,
              </if>
              <if test="sku != null">
				  sku,
              </if>
              <if test="weight != null">
				  weight,
              </if>
              <if test="maturity != null">
				  maturity,
              </if>
              <if test="orderNo != null">
				  order_no,
              </if>
              <if test="categoryId != null">
				  category_id,
              </if>
              <if test="amount != null">
				  amount,
              </if>
              <if test="price != null">
				  price,
              </if>
              <if test="originalPrice != null">
				  original_price,
              </if>
              <if test="picturePath != null">
				  picture_path,
              </if>
              <if test="addTime != null">
				  add_time,
              </if>
              <if test="storageLocation != null">
				  storage_location,
              </if>
              <if test="suitId != null">
				  suit_id,
              </if>
              <if test="suitName != null">
				  suit_name,
              </if>
              <if test="suitAmount != null">
				  suit_amount,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="rebateType != null">
				  rebate_type,
              </if>
              <if test="rebateNumber != null">
				  rebate_number,
              </if>
              <if test="mPrice != null">
				  m_price,
              </if>
              <if test="volume != null">
				  volume,
              </if>
              <if test="weightNum != null">
				  weight_num,
              </if>
              <if test="useCoupon != null">
				  use_coupon,
              </if>
              <if test="maxThreshold != null">
				  max_threshold,
              </if>
              <if test="totalPrice != null">
				  total_price,
              </if>
              <if test="productType != null">
				  product_type,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="actualTotalPrice != null">
				  actual_total_price,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="skuName != null">
				  sku_name,
              </if>
              <if test="info != null">
				  info,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="pdName != null">
				#{pdName,jdbcType=VARCHAR},
              </if>
              <if test="sku != null">
				#{sku,jdbcType=VARCHAR},
              </if>
              <if test="weight != null">
				#{weight,jdbcType=VARCHAR},
              </if>
              <if test="maturity != null">
				#{maturity,jdbcType=VARCHAR},
              </if>
              <if test="orderNo != null">
				#{orderNo,jdbcType=VARCHAR},
              </if>
              <if test="categoryId != null">
				#{categoryId,jdbcType=INTEGER},
              </if>
              <if test="amount != null">
				#{amount,jdbcType=INTEGER},
              </if>
              <if test="price != null">
				#{price,jdbcType=DOUBLE},
              </if>
              <if test="originalPrice != null">
				#{originalPrice,jdbcType=DOUBLE},
              </if>
              <if test="picturePath != null">
				#{picturePath,jdbcType=VARCHAR},
              </if>
              <if test="addTime != null">
				#{addTime,jdbcType=TIMESTAMP},
              </if>
              <if test="storageLocation != null">
				#{storageLocation,jdbcType=TINYINT},
              </if>
              <if test="suitId != null">
				#{suitId,jdbcType=INTEGER},
              </if>
              <if test="suitName != null">
				#{suitName,jdbcType=VARCHAR},
              </if>
              <if test="suitAmount != null">
				#{suitAmount,jdbcType=INTEGER},
              </if>
              <if test="status != null">
				#{status,jdbcType=INTEGER},
              </if>
              <if test="rebateType != null">
				#{rebateType,jdbcType=INTEGER},
              </if>
              <if test="rebateNumber != null">
				#{rebateNumber,jdbcType=DOUBLE},
              </if>
              <if test="mPrice != null">
				#{mPrice,jdbcType=DOUBLE},
              </if>
              <if test="volume != null">
				#{volume,jdbcType=VARCHAR},
              </if>
              <if test="weightNum != null">
				#{weightNum,jdbcType=DOUBLE},
              </if>
              <if test="useCoupon != null">
				#{useCoupon,jdbcType=TINYINT},
              </if>
              <if test="maxThreshold != null">
				#{maxThreshold,jdbcType=INTEGER},
              </if>
              <if test="totalPrice != null">
				#{totalPrice,jdbcType=DOUBLE},
              </if>
              <if test="productType != null">
				#{productType,jdbcType=INTEGER},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="actualTotalPrice != null">
				#{actualTotalPrice,jdbcType=DOUBLE},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="skuName != null">
				#{skuName,jdbcType=VARCHAR},
              </if>
              <if test="info != null">
				#{info,jdbcType=VARCHAR},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.order.OrderItem" >
        UPDATE order_item t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>
    <update id="updateSelectiveByOrderNo">
        UPDATE order_item t
        <include refid="whereColumnByUpdate"></include>
        where t.order_no = #{orderNo,jdbcType=VARCHAR}
    </update>


    <!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.order.OrderItem" >
        DELETE FROM order_item t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>




    <select id="selectByMasterOrderNo" resultMap="orderItemEntityResultMap">
        SELECT <include refid="orderItemColumns" />
        FROM order_item t
        inner join orders o on t.order_no = o.order_no
        inner join order_relation ore on o.order_no = ore.order_no
        WHERE ore.master_order_no = #{masterOrderNo}
    </select>

    <select id="selectBatchTimingOrderQuantity" resultMap="orderItemResultMap">
        select oi.amount, oi.order_no, oi.status, oi.id
        FROM order_item oi
        WHERE oi.order_no in
        <foreach collection="timingOrderNos" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>