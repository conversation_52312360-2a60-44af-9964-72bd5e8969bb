<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.order.OrderRelationMapper">
    <!-- 结果集映射 -->
    <resultMap id="orderRelationResultMap" type="net.summerfarm.manage.infrastructure.model.order.OrderRelation">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="master_order_no" property="masterOrderNo" jdbcType="VARCHAR"/>
		<result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
		<result column="precision_delivery_fee" property="precisionDeliveryFee" jdbcType="DOUBLE"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="orderRelationColumns">
          t.id,
          t.master_order_no,
          t.order_no,
          t.create_time,
          t.update_time,
          t.precision_delivery_fee
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="masterOrderNo != null and masterOrderNo !=''">
                AND t.master_order_no = #{masterOrderNo}
            </if>
			<if test="orderNo != null and orderNo !=''">
                AND t.order_no = #{orderNo}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
			<if test="precisionDeliveryFee != null">
                AND t.precision_delivery_fee = #{precisionDeliveryFee}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="masterOrderNo != null">
                    t.master_order_no = #{masterOrderNo},
                </if>
                <if test="orderNo != null">
                    t.order_no = #{orderNo},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
                <if test="precisionDeliveryFee != null">
                    t.precision_delivery_fee = #{precisionDeliveryFee},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="orderRelationResultMap" >
        SELECT <include refid="orderRelationColumns" />
        FROM order_relation t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.order.param.query.OrderRelationQueryParam"  resultType="net.summerfarm.manage.domain.order.entity.OrderRelationEntity" >
        SELECT
            t.id id,
            t.master_order_no masterOrderNo,
            t.order_no orderNo,
            t.create_time createTime,
            t.update_time updateTime,
            t.precision_delivery_fee precisionDeliveryFee
        FROM order_relation t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.order.param.query.OrderRelationQueryParam" resultMap="orderRelationResultMap" >
        SELECT <include refid="orderRelationColumns" />
        FROM order_relation t
        <include refid="whereColumnBySelect"></include>
    </select>
    <select id="selectByOrderNoBatch" resultMap="orderRelationResultMap">
        select
        <include refid="orderRelationColumns"/>
        from order_relation t
        where t.order_no in
        <foreach collection="orders" open="(" close=") " separator="," item="orderNo">
            #{orderNo}
        </foreach>
    </select>
    <select id="selectOrderNoByMasterOrderNo" resultType="java.lang.String">
        select order_no from order_relation where master_order_no = #{masterOrderNo}
    </select>


    <!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.order.OrderRelation" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO order_relation
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="masterOrderNo != null">
				  master_order_no,
              </if>
              <if test="orderNo != null">
				  order_no,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
              <if test="precisionDeliveryFee != null">
				  precision_delivery_fee,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="masterOrderNo != null">
				#{masterOrderNo,jdbcType=VARCHAR},
              </if>
              <if test="orderNo != null">
				#{orderNo,jdbcType=VARCHAR},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
              <if test="precisionDeliveryFee != null">
				#{precisionDeliveryFee,jdbcType=DOUBLE},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.order.OrderRelation" >
        UPDATE order_relation t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.order.OrderRelation" >
        DELETE FROM order_relation t
		WHERE t.id = #{id,jdbcType=NUMERIC}
    </delete>



</mapper>