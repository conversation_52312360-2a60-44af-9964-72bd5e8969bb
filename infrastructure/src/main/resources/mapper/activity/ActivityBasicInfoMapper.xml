<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.manage.infrastructure.mapper.activity.ActivityBasicInfoMapper">
    <!-- 结果集映射 -->
    <resultMap id="activityBasicInfoResultMap" type="net.summerfarm.manage.infrastructure.model.activity.ActivityBasicInfo">
		<id column="id" property="id" jdbcType="NUMERIC"/>
		<result column="name" property="name" jdbcType="VARCHAR"/>
		<result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
		<result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
		<result column="is_permanent" property="isPermanent" jdbcType="TINYINT"/>
		<result column="status" property="status" jdbcType="TINYINT"/>
		<result column="need_pre" property="needPre" jdbcType="TINYINT"/>
		<result column="pre_start_time" property="preStartTime" jdbcType="TIMESTAMP"/>
		<result column="pre_end_time" property="preEndTime" jdbcType="TIMESTAMP"/>
		<result column="type" property="type" jdbcType="INTEGER"/>
		<result column="tag" property="tag" jdbcType="TINYINT"/>
		<result column="remark" property="remark" jdbcType="VARCHAR"/>
		<result column="creator_id" property="creatorId" jdbcType="INTEGER"/>
		<result column="updater_id" property="updaterId" jdbcType="INTEGER"/>
		<result column="del_flag" property="delFlag" jdbcType="TINYINT"/>
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 列定义 -->
    <sql id="activityBasicInfoColumns">
          t.id,
          t.name,
          t.start_time,
          t.end_time,
          t.is_permanent,
          t.status,
          t.need_pre,
          t.pre_start_time,
          t.pre_end_time,
          t.type,
          t.tag,
          t.remark,
          t.creator_id,
          t.updater_id,
          t.del_flag,
          t.create_time,
          t.update_time
    </sql>

    <!-- 查询条件SQL -->
    <sql id="whereColumnBySelect">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
			<if test="id != null">
                AND t.id = #{id}
            </if>
			<if test="name != null and name !=''">
                AND t.name = #{name}
            </if>
			<if test="startTime != null">
                AND t.start_time = #{startTime}
            </if>
			<if test="endTime != null">
                AND t.end_time = #{endTime}
            </if>
			<if test="isPermanent != null">
                AND t.is_permanent = #{isPermanent}
            </if>
			<if test="status != null">
                AND t.status = #{status}
            </if>
			<if test="needPre != null">
                AND t.need_pre = #{needPre}
            </if>
			<if test="preStartTime != null">
                AND t.pre_start_time = #{preStartTime}
            </if>
			<if test="preEndTime != null">
                AND t.pre_end_time = #{preEndTime}
            </if>
			<if test="type != null">
                AND t.type = #{type}
            </if>
			<if test="tag != null">
                AND t.tag = #{tag}
            </if>
			<if test="remark != null and remark !=''">
                AND t.remark = #{remark}
            </if>
			<if test="creatorId != null">
                AND t.creator_id = #{creatorId}
            </if>
			<if test="updaterId != null">
                AND t.updater_id = #{updaterId}
            </if>
			<if test="delFlag != null">
                AND t.del_flag = #{delFlag}
            </if>
			<if test="createTime != null">
                AND t.create_time = #{createTime}
            </if>
			<if test="updateTime != null">
                AND t.update_time = #{updateTime}
            </if>
        </trim>
    </sql>

	<!-- 修改字段SQL -->
	<sql id="whereColumnByUpdate">
        <trim prefix="SET" suffixOverrides=",">
                <if test="name != null">
                    t.name = #{name},
                </if>
                <if test="startTime != null">
                    t.start_time = #{startTime},
                </if>
                <if test="endTime != null">
                    t.end_time = #{endTime},
                </if>
                <if test="isPermanent != null">
                    t.is_permanent = #{isPermanent},
                </if>
                <if test="status != null">
                    t.status = #{status},
                </if>
                <if test="needPre != null">
                    t.need_pre = #{needPre},
                </if>
                <if test="preStartTime != null">
                    t.pre_start_time = #{preStartTime},
                </if>
                <if test="preEndTime != null">
                    t.pre_end_time = #{preEndTime},
                </if>
                <if test="type != null">
                    t.type = #{type},
                </if>
                <if test="tag != null">
                    t.tag = #{tag},
                </if>
                <if test="remark != null">
                    t.remark = #{remark},
                </if>
                <if test="creatorId != null">
                    t.creator_id = #{creatorId},
                </if>
                <if test="updaterId != null">
                    t.updater_id = #{updaterId},
                </if>
                <if test="delFlag != null">
                    t.del_flag = #{delFlag},
                </if>
                <if test="createTime != null">
                    t.create_time = #{createTime},
                </if>
                <if test="updateTime != null">
                    t.update_time = #{updateTime},
                </if>
        </trim>
    </sql>

	<!-- 根据主键ID获取数据 -->
	<select id="selectById" parameterType="java.lang.Long" resultMap="activityBasicInfoResultMap" >
        SELECT <include refid="activityBasicInfoColumns" />
        FROM activity_basic_info t
		WHERE t.id = #{id}
    </select>

    <!-- 查询列表可以根据分页进行查询 -->
    <select id="getPage" parameterType="net.summerfarm.manage.domain.activity.param.query.ActivityBasicInfoQueryParam"  resultType="net.summerfarm.manage.domain.activity.entity.ActivityBasicInfoEntity" >
        SELECT
            t.id id,
            t.name name,
            t.start_time startTime,
            t.end_time endTime,
            t.is_permanent isPermanent,
            t.status status,
            t.need_pre needPre,
            t.pre_start_time preStartTime,
            t.pre_end_time preEndTime,
            t.type type,
            t.tag tag,
            t.remark remark,
            t.creator_id creatorId,
            t.updater_id updaterId,
            t.del_flag delFlag,
            t.create_time createTime,
            t.update_time updateTime
        FROM activity_basic_info t
        <include refid="whereColumnBySelect" />
            ORDER BY t.id DESC
    </select>


    <!-- 根据条件查询对象 -->
    <select id="selectByCondition" parameterType="net.summerfarm.manage.domain.activity.param.query.ActivityBasicInfoQueryParam" resultMap="activityBasicInfoResultMap" >
        SELECT <include refid="activityBasicInfoColumns" />
        FROM activity_basic_info t
        <include refid="whereColumnBySelect"></include>
    </select>



	<!-- 新增并设置主键ID判断哪些列不为空时，则进行插入 -->
	<insert id="insertSelective" parameterType="net.summerfarm.manage.infrastructure.model.activity.ActivityBasicInfo" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO activity_basic_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
              <if test="id != null">
				  id,
              </if>
              <if test="name != null">
				  name,
              </if>
              <if test="startTime != null">
				  start_time,
              </if>
              <if test="endTime != null">
				  end_time,
              </if>
              <if test="isPermanent != null">
				  is_permanent,
              </if>
              <if test="status != null">
				  status,
              </if>
              <if test="needPre != null">
				  need_pre,
              </if>
              <if test="preStartTime != null">
				  pre_start_time,
              </if>
              <if test="preEndTime != null">
				  pre_end_time,
              </if>
              <if test="type != null">
				  type,
              </if>
              <if test="tag != null">
				  tag,
              </if>
              <if test="remark != null">
				  remark,
              </if>
              <if test="creatorId != null">
				  creator_id,
              </if>
              <if test="updaterId != null">
				  updater_id,
              </if>
              <if test="delFlag != null">
				  del_flag,
              </if>
              <if test="createTime != null">
				  create_time,
              </if>
              <if test="updateTime != null">
				  update_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
              <if test="id != null">
				#{id,jdbcType=NUMERIC},
              </if>
              <if test="name != null">
				#{name,jdbcType=VARCHAR},
              </if>
              <if test="startTime != null">
				#{startTime,jdbcType=TIMESTAMP},
              </if>
              <if test="endTime != null">
				#{endTime,jdbcType=TIMESTAMP},
              </if>
              <if test="isPermanent != null">
				#{isPermanent,jdbcType=TINYINT},
              </if>
              <if test="status != null">
				#{status,jdbcType=TINYINT},
              </if>
              <if test="needPre != null">
				#{needPre,jdbcType=TINYINT},
              </if>
              <if test="preStartTime != null">
				#{preStartTime,jdbcType=TIMESTAMP},
              </if>
              <if test="preEndTime != null">
				#{preEndTime,jdbcType=TIMESTAMP},
              </if>
              <if test="type != null">
				#{type,jdbcType=INTEGER},
              </if>
              <if test="tag != null">
				#{tag,jdbcType=TINYINT},
              </if>
              <if test="remark != null">
				#{remark,jdbcType=VARCHAR},
              </if>
              <if test="creatorId != null">
				#{creatorId,jdbcType=INTEGER},
              </if>
              <if test="updaterId != null">
				#{updaterId,jdbcType=INTEGER},
              </if>
              <if test="delFlag != null">
				#{delFlag,jdbcType=TINYINT},
              </if>
              <if test="createTime != null">
				#{createTime,jdbcType=TIMESTAMP},
              </if>
              <if test="updateTime != null">
				#{updateTime,jdbcType=TIMESTAMP},
              </if>
        </trim>
    </insert>

  	<!-- 根据主键ID进行修改 -->
  	<update id="updateSelectiveById" parameterType="net.summerfarm.manage.infrastructure.model.activity.ActivityBasicInfo" >
        UPDATE activity_basic_info t
        <include refid="whereColumnByUpdate"></include>
        <where>
                t.id = #{id,jdbcType=NUMERIC}
        </where>
    </update>



	<!-- 根据主键ID进行物理删除 -->
	<delete id="remove" parameterType="net.summerfarm.manage.infrastructure.model.activity.ActivityBasicInfo" >
        DELETE FROM activity_basic_info
		WHERE id = #{id,jdbcType=NUMERIC}
    </delete>



    <select id="listByScope"
            resultType="net.summerfarm.manage.domain.activity.valueObject.ActivityItemScopeValueObject">
        /*FORCE_MASTER*/
        select aic.basic_info_id basicInfoId,aic.id itemConfigId, asco.scope_id scopeId
        from activity_basic_info abi
        left join activity_item_config aic on abi.id = aic.basic_info_id
        left join activity_scope_config asco on abi.id = asco.basic_info_id
        <where>
            abi.del_flag = 0 and aic.del_flag = 0 and asco.del_flag = 0
            <if test="type != null">
                and abi.type = #{type}
            </if>
            <if test="list != null and list.size > 0">
                and (scope_id,scope_type) in
                <foreach collection="list" item="item" open="(" separator="," close=")">
                    (#{item.scopeId},#{item.scopeType})
                </foreach>
            </if>
            <if test="activityStatus != null">
                <choose>
                    <when test="activityStatus == 0">
                        and (abi.start_time &lt; now()
                        or (abi.start_time &lt;= now() and abi.end_time &gt;= now() and status = 0)
                        or (abi.is_permanent = 1 and status = 0))
                    </when>
                    <when test="activityStatus == 1">
                        and ((abi.start_time &lt;= now() and abi.end_time &gt;= now())
                        or abi.is_permanent = 1)
                        and status = 1
                    </when>
                    <when test="activityStatus == 2">
                        and abi.end_time &lt; now()
                    </when>
                </choose>
            </if>
        </where>
        order by abi.id desc
    </select>


</mapper>