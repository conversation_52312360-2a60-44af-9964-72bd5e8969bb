package net.summerfarm.manage.application.test.service;

import net.summerfarm.client.req.customization.CustomizationRequestReq;
import net.summerfarm.manage.application.inbound.provider.customization.CustomizationRequestProviderImpl;
import net.summerfarm.manage.starter.Application;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.Assert.*;

/**
 * CustomizationRequestProviderImpl 集成测试
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class CustomizationRequestProviderImplTest {

    @Resource
    private CustomizationRequestProviderImpl customizationRequestProvider;

    /**
     * 测试复制SKU并保存定制需求 - 正常流程
     */
    @Test
    public void testCopySkuAndSaveCustomizationRequest_Success() {
        // Given
        CustomizationRequestReq req = createTestRequest();

        // When
        DubboResponse<List<String>> result = customizationRequestProvider.copySkuAndSaveCustomizationRequest(req);

        // Then
        assertNotNull("返回结果不能为空", result);
        assertTrue("操作应该成功", result.isSuccess());
        assertNotNull("返回的SKU列表不能为空", result.getData());

        // 打印结果用于调试
        System.out.println("复制成功的SKU列表: " + result.getData());
    }

    /**
     * 创建测试用的CustomizationRequestReq对象
     */
    private CustomizationRequestReq createTestRequest() {
        CustomizationRequestReq req = new CustomizationRequestReq();
        req.setPdId(2578L);
        req.setWeightLike("颜色1个");
        req.setAreaNo(1001);
        req.setStoreName("测试门店");
        req.setAccountName("designer001");
        req.setMId(349521L);
//        req.setMasterOrderNo("ORDER" + System.currentTimeMillis()); // 使用时间戳避免重复
        req.setColorCount(1);
        req.setReferenceImage("http://example.com/ref.jpg");
        req.setLogoImage("http://example.com/logo.jpg");
        req.setLogoSize("中");
        req.setStoreRemark("客户要求红色主题");
        return req;
    }
}
