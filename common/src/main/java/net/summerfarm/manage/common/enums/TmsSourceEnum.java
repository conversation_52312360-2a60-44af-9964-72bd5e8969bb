package net.summerfarm.manage.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.summerfarm.enums.OrderTypeEnum;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TmsSourceEnum {

    /**
     * 城配(200-299)
     */
    XM_MALL(200, "鲜沐商城"),
    XM_AFTER_SALE(201, "鲜沐售后"),
    XM_SAMPLE_APPLY(202, "鲜沐样品申请"),
    XM_MALL_TIMING(203, "鲜沐商城省心送订单"),
    POP_MALL(230, "POP商城"),
    POP_AFTER_SALE(231, "POP售后"),
    ;
    private Integer value;
    private String content;

    /**
     * 返回对应业务
     * @param type 订单类型
     * @return 业务编号
     */
    public static Integer getDistOrderSource(Integer type){
        if (type.equals(OrderTypeEnum.TIMING.getId())){
            return TmsSourceEnum.XM_MALL_TIMING.getValue();
        }else if (type.equals(OrderTypeEnum.POP.getId())){
            return TmsSourceEnum.POP_MALL.getValue();
        } else {
            return TmsSourceEnum.XM_MALL.getValue();
        }
    }
}
