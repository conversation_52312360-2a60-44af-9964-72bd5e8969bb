
package net.summerfarm.manage.common.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import net.xianmu.common.exception.BizException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


public class ExcelUtils {
    private static final Logger logger = LoggerFactory.getLogger(ExcelUtils.class);

    private static final int BUFFER_SIZE = 8192;

    private ExcelUtils() {
    }
    /**
     * 获得文件的InputStream
     *
     * @param fileName
     * @return
     */
    public static InputStream getExcelFileInputStream(Class clazz, String fileName) {

        InputStream inputStream = clazz.getClassLoader()
                .getResourceAsStream("excel/" + fileName);

        return inputStream;
    }
    /**
     * 文件删除
     *
     * @param filePath 文件路径
     */
    public static void deleteFile(String filePath) {
        try {
            Files.delete(Paths.get(filePath));
        } catch (IOException e) {
            logger.error("删除文件失败", e);
        }
    }

    /**
     * 获得文件缓存名称
     *
     * @return
     */
    public static String tempExcelFilePath() {
        return System.getProperty("user.dir") + File.separator + tempExcelFileName();
    }

    /**
     * xlsx类型excel文件临时名称
     *
     * @return 文件名称
     */
    public static String tempExcelFileName() {
        return System.currentTimeMillis() + ExcelTypeEnum.XLSX.getValue();
    }

    /**
     * 字节 ==> 输出流
     *
     * @param bytes        字节数组
     * @param outputStream 输出流
     */
    public static void copyBytesToOutputStream(byte[] bytes, OutputStream outputStream) {
        Objects.requireNonNull(bytes);

        byte[] tempByte = new byte[BUFFER_SIZE];
        int n;
        try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
            while ((n = inputStream.read(tempByte)) > 0) {
                outputStream.write(tempByte, 0, n);
            }
        } catch (IOException e) {
            logger.error("", e);
        }
    }

    public static <T> List<T> read(String filePath, final Class<?> clazz) {
        File f = new File(filePath);
        try (FileInputStream fis = new FileInputStream(f)) {
            return read(fis, clazz);
        } catch (FileNotFoundException e) {
            logger.error("文件{}不存在", filePath, e);
        } catch (IOException e) {
            logger.error("文件读取出错", e);
        }
        return null;
    }

    public static <T> List<T> read(InputStream inputStream, final Class<?> clazz) {
        if (inputStream == null) {
            throw new BizException ("解析出错了，文件流是null");
        }
        // 有个很重要的点 DataListener 不能被spring管理，要每次读取excel都要new,然后里面用到spring可以构造方法传进去
        DataListener<T> listener = new DataListener<>();
        // 这里 需要指定读用哪个class去读，然后读取第一个sheet 文件流会自动关闭
        EasyExcel.read(inputStream, clazz, listener).sheet().doRead();
        return listener.getRows();
    }

    public static void write(String outFile, List<?> list) {
        Class<?> clazz = list.get(0).getClass();
        EasyExcel.write(outFile, clazz).sheet().doWrite(list);
    }

    public static void write(String outFile, List<?> list, String sheetName) {
        Class<?> clazz = list.get(0).getClass();
        EasyExcel.write(outFile, clazz).sheet(sheetName).doWrite(list);
    }

    public static void write(OutputStream outputStream, List<?> list, String sheetName) {
        Class<?> clazz = list.get(0).getClass();
        // sheetName为sheet的名字，默认写第一个sheet
        EasyExcel.write(outputStream, clazz).sheet(sheetName).doWrite(list);
    }


}

class DataListener<T> extends AnalysisEventListener<T> {

    private static final Logger logger = LoggerFactory.getLogger(DataListener.class);

    private final List<T> rows = new ArrayList<>();

    @Override
    public void invoke(T t, AnalysisContext analysisContext) {
        rows.add(t);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        logger.info("解析完成！读取{}行", rows.size());
    }

    public List<T> getRows() {
        return rows;
    }
}
