package net.summerfarm.manage.common.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/9 10:53
 */
public enum DingTalkMsgTypeEnum {

    /**
     * 普通文本
     */
    TXT("text"),

    /**
     * markdown
     */
    MARKDOWN("markdown"),

    /**
     * 链接类型
     */
    LINK("link");


    private String type;

    DingTalkMsgTypeEnum() {
    }

    DingTalkMsgTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
