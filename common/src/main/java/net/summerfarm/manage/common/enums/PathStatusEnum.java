package net.summerfarm.manage.common.enums;

import lombok.Getter;

/**
 * Description: <br/>
 * date: 2022/5/10 10:32<br/>
 *
 * <AUTHOR> />
 */
@Getter
public enum PathStatusEnum {
    toBePicked(0,"待捡货"),
    pickedComplete(1,"配送中"),
    sendComplete(2,"完成配送"),
    ;

    private Integer code;
    private String des;

    PathStatusEnum(Integer code, String des) {
        this.code = code;
        this.des = des;
    }
}
