package net.summerfarm.manage.application.service.account.converter;

import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantSubAccountVO;
import net.summerfarm.manage.domain.merchant.entity.MerchantSubAccountEntity;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class MerchantSubAccountConverter {


    private MerchantSubAccountConverter() {
        // 无需实现
    }

    public static List<MerchantSubAccountVO> toMerchantSubAccountVOList(List<MerchantStoreAccountResultResp> merchantStoreAccountResultRespList) {
        if (merchantStoreAccountResultRespList == null) {
            return Collections.emptyList();
        }
        List<MerchantSubAccountVO> merchantSubAccountVOList = new ArrayList<>();
        for (MerchantStoreAccountResultResp merchantStoreAccountResultResp : merchantStoreAccountResultRespList) {
            merchantSubAccountVOList.add(toMerchantSubAccountVO(merchantStoreAccountResultResp));
        }
        return merchantSubAccountVOList;
    }

    public static MerchantSubAccountVO toMerchantSubAccountVO(MerchantStoreAccountResultResp merchantStoreAccountResultResp) {
        if (merchantStoreAccountResultResp == null) {
            return null;
        }
        MerchantSubAccountVO merchantSubAccountVO = new MerchantSubAccountVO();
        merchantSubAccountVO.setType(merchantStoreAccountResultResp.getType());
        merchantSubAccountVO.setPhone(merchantStoreAccountResultResp.getPhone());
        merchantSubAccountVO.setStatus(merchantStoreAccountResultResp.getStatus());
        merchantSubAccountVO.setDeleteFlag(merchantStoreAccountResultResp.getDeleteFlag());
        merchantSubAccountVO.setRegisterTime(merchantStoreAccountResultResp.getRegisterTime());
        merchantSubAccountVO.setAuditTime(merchantStoreAccountResultResp.getAuditTime());
        merchantSubAccountVO.setUpdateTime(merchantStoreAccountResultResp.getUpdateTime());
        merchantSubAccountVO.setContact(merchantStoreAccountResultResp.getAccountName());
        merchantSubAccountVO.setAccountId(merchantStoreAccountResultResp.getXmAccountId());
// Not mapped TO fields:
// accountId
// mId
// contact
// unionid
// openid
// mpOpenid
// popView
// firstPopView
// cashAmount
// cashUpdateTime
// loginTime
// lastOrderTime
// mInfo
// auditUser
// Not mapped FROM fields:
// id
// tenantId
// storeId
// accountName
// openId
// unionId
// createTime
// lastLoginTime
// oaOpenId
        return merchantSubAccountVO;
    }

    public static List<MerchantStoreAccountResultResp> toMerchantStoreAccountResultRespList(List<MerchantSubAccountVO> merchantSubAccountVOList) {
        if (merchantSubAccountVOList == null) {
            return Collections.emptyList();
        }
        List<MerchantStoreAccountResultResp> merchantStoreAccountResultRespList = new ArrayList<>();
        for (MerchantSubAccountVO merchantSubAccountVO : merchantSubAccountVOList) {
            merchantStoreAccountResultRespList.add(toMerchantStoreAccountResultResp(merchantSubAccountVO));
        }
        return merchantStoreAccountResultRespList;
    }

    public static MerchantStoreAccountResultResp toMerchantStoreAccountResultResp(MerchantSubAccountVO merchantSubAccountVO) {
        if (merchantSubAccountVO == null) {
            return null;
        }
        MerchantStoreAccountResultResp merchantStoreAccountResultResp = new MerchantStoreAccountResultResp();
        merchantStoreAccountResultResp.setPhone(merchantSubAccountVO.getPhone());
        merchantStoreAccountResultResp.setType(merchantSubAccountVO.getType());
        merchantStoreAccountResultResp.setRegisterTime(merchantSubAccountVO.getRegisterTime());
        merchantStoreAccountResultResp.setAuditTime(merchantSubAccountVO.getAuditTime());
        merchantStoreAccountResultResp.setStatus(merchantSubAccountVO.getStatus());
        merchantStoreAccountResultResp.setUpdateTime(merchantSubAccountVO.getUpdateTime());
        merchantStoreAccountResultResp.setDeleteFlag(merchantSubAccountVO.getDeleteFlag());
        merchantStoreAccountResultResp.setAccountName(merchantSubAccountVO.getContact());
// Not mapped TO fields:
// id
// tenantId
// storeId
// accountName
// openId
// unionId
// createTime
// lastLoginTime
// oaOpenId
// Not mapped FROM fields:
// accountId
// mId
// contact
// unionid
// openid
// mpOpenid
// popView
// firstPopView
// cashAmount
// cashUpdateTime
// loginTime
// lastOrderTime
// mInfo
// auditUser
        return merchantStoreAccountResultResp;
    }
}
