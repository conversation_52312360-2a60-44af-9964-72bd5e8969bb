package net.summerfarm.manage.application.service.merchant;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.merchant.input.MajorMerchantQueryInput;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MajorCustomerMerchantVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantVO;
import net.summerfarm.manage.common.input.merchant.MerchantQueryInput;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;

import java.util.List;

/**
 *
 * @date 2023-10-19 15:25:01
 * @version 1.0
 *
 */
public interface MerchantQueryService {

    /**
     * @description: 新增
     * @return MerchantEntity
     **/
    PageInfo<MerchantVO> getPage(MerchantQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    MerchantVO getDetail(Long mid);


    MerchantEntity getByMid(Long mid);
    /**
     * 根据查询条件获取主要商家列表
     *
     * @param selectKeys 查询条件
     * @return 主要商家列表
     */
    PageInfo<MajorCustomerMerchantVO> majorMerchantList(MajorMerchantQueryInput selectKeys);
}