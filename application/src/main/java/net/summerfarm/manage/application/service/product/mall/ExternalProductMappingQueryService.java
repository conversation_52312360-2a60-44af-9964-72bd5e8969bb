package net.summerfarm.manage.application.service.product.mall;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.entity.CategoryEntity;
import net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity;
import net.summerfarm.manage.application.inbound.controller.product.input.ExternalProductMappingQueryInput;

import java.util.List;

/**
 *
 * @date 2024-11-15 14:13:27
 * @version 1.0
 *
 */
public interface ExternalProductMappingQueryService {

    /**
     * @description: 分页
     * @return ExternalProductMappingEntity
     **/
    PageInfo<ExternalProductMappingEntity> getPage(ExternalProductMappingQueryInput input);

    /**
     * @description: 详情
     * @return: java.lang.Boolean
     **/
    ExternalProductMappingEntity getDetail(Long id);

    /**
     * 查询待绑定商品列表
     *
     * <AUTHOR>
     * @date 2024/11/19 15:04
     */
    PageInfo<ExternalProductMappingEntity> getPageUnmapped(ExternalProductMappingQueryInput input);

    /**
     * 查询待绑定商品类目列表
     *
     * <AUTHOR>
     * @date 2024/11/19 15:04
     */
    List<CategoryEntity> getListUnmappedCategory(ExternalProductMappingQueryInput input);

    /**
     * 查询待绑定商品列表
     *
     * <AUTHOR>
     * @date 2024/11/19 15:04
     */
    PageInfo<ExternalProductMappingEntity> getPageMapped(ExternalProductMappingQueryInput input);

    /**
     * 查询待绑定商品类目列表
     *
     * <AUTHOR>
     * @date 2024/11/19 15:04
     */
    List<CategoryEntity> getListMappedCategory(ExternalProductMappingQueryInput input);

    /**
     * 分页查询POP商品映射关系
     *
     * <AUTHOR>
     * @date 2024/12/4 15:47
     */
    PageInfo<ExternalProductMappingEntity> pageQueryExternalProductMapping(ExternalProductMappingQueryInput input);
}