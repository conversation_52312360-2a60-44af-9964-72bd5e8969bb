package net.summerfarm.manage.application.service.wx;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.enums.OrderTypeEnum;
import net.summerfarm.manage.common.constants.ApiConsts;
import net.summerfarm.manage.common.constants.PaymentConsts;
import net.summerfarm.manage.common.dto.wx.UploadShippingInfoReq;
import net.summerfarm.manage.common.dto.wx.UploadShippingInfoReq.OrderKey;
import net.summerfarm.manage.common.dto.wx.UploadShippingInfoReq.Payer;
import net.summerfarm.manage.common.dto.wx.UploadShippingInfoReq.Shipping;
import net.summerfarm.manage.common.dto.wx.UploadShippingInfoResult;
import net.summerfarm.manage.common.enums.payment.PaymentEnums;
import net.summerfarm.manage.domain.order.entity.OrderItemEntity;
import net.summerfarm.manage.domain.order.entity.OrdersEntity;
import net.summerfarm.manage.domain.order.entity.WxShippingInfoUploadRecordEntity;
import net.summerfarm.manage.domain.order.param.command.WxShippingInfoUploadRecordCommandParam;
import net.summerfarm.manage.domain.order.param.query.OrderItemQueryParam;
import net.summerfarm.manage.domain.order.param.query.WxShippingInfoUploadRecordQueryParam;
import net.summerfarm.manage.domain.order.repository.OrderItemQueryRepository;
import net.summerfarm.manage.domain.order.repository.OrdersQueryRepository;
import net.summerfarm.manage.domain.order.repository.WxShippingInfoUploadRecordCommandRepository;
import net.summerfarm.manage.domain.order.repository.WxShippingInfoUploadRecordQueryRepository;
import net.summerfarm.manage.domain.payment.entity.PaymentEntity;
import net.summerfarm.manage.domain.payment.param.query.PaymentQueryParam;
import net.summerfarm.manage.domain.payment.repository.MasterPaymentQueryRepository;
import net.summerfarm.manage.domain.payment.repository.PaymentQueryRepository;
import net.summerfarm.manage.facade.auth.AuthWechatQueryFacade;
import net.summerfarm.manage.facade.merchant.MerchantAccountFacade;
import net.xianmu.common.enums.base.auth.WechatMiniProgramChannelEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreAccountResultResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * @author: xiaowk
 * @time: 2023/7/11 下午5:48
 */
@Service
@Slf4j
public class WeixinShippingServiceImpl {

    @Resource
    private MasterPaymentQueryRepository masterPaymentQueryRepository;
    @Resource
    private PaymentQueryRepository paymentQueryRepository;
    @Resource
    private AuthWechatQueryFacade authWechatQueryFacade;
    @Resource
    private MerchantAccountFacade merchantAccountFacade;
    @Resource
    private OrdersQueryRepository ordersQueryRepository;
    @Resource
    private OrderItemQueryRepository orderItemQueryRepository;
    @Resource
    private WxShippingInfoUploadRecordQueryRepository wxShippingInfoUploadRecordQueryRepository;
    @Resource
    private WxShippingInfoUploadRecordCommandRepository wxShippingInfoUploadRecordCommandRepository;




    private void uploadShippingInfoCommon(String transactionNumber, String orderNo, Integer ordersType) {
        if (StringUtils.isBlank(transactionNumber)) {
            log.error("【小程序发货信息管理】【数据异常】支付流水号不存在");
            return;
        }

        WxShippingInfoUploadRecordQueryParam param = new WxShippingInfoUploadRecordQueryParam();
        param.setTransactionId(transactionNumber);
        List<WxShippingInfoUploadRecordEntity> wxShippingInfoUploadRecordEntities = wxShippingInfoUploadRecordQueryRepository.selectByCondition(param);
        if (CollectionUtil.isNotEmpty(wxShippingInfoUploadRecordEntities)) {
            log.warn("【小程序发货信息管理】【数据异常】订单已上传发货信息，transactionNumber={}", transactionNumber);
            return;
        }
        try {
            String accessToken = "";
            if(30 == ordersType) {
                accessToken = authWechatQueryFacade.queryChannelToken(WechatMiniProgramChannelEnum.POP_MALL_MP.channelCode);
            } else {
                accessToken = authWechatQueryFacade.queryChannelToken(WechatMiniProgramChannelEnum.XM_MALL_MP.channelCode);
            }

            OrdersEntity ordersEntity = ordersQueryRepository.selectByOrderNo(orderNo);
            MerchantStoreAccountResultResp account = merchantAccountFacade.selectMerchantAccountByAccountId(ordersEntity.getAccountId());
            if (account == null) {
                log.warn("【小程序发货信息管理】【数据异常】订单支付者信息不存在！orderNo：{}", orderNo);
                return;
            }

            String titles = "";
            if(OrderTypeEnum.VIRTUAL_GOODS.getId().equals(ordersType)) {
                titles = "优惠卡-奶油卡";
            } else {
                OrderItemQueryParam itemQueryParam = new OrderItemQueryParam();
                itemQueryParam.setOrderNo(orderNo);
                List<OrderItemEntity> orderItemVOS = orderItemQueryRepository.selectByCondition(itemQueryParam);
                if (CollectionUtil.isEmpty(orderItemVOS)) {
                    log.warn("【小程序发货信息管理】【数据异常】订单项不存在！orderNo：{}", orderNo);
                    return;
                }
                orderItemVOS = orderItemVOS.subList(0, Math.min(5, orderItemVOS.size()));
                titles = StringUtils.join(orderItemVOS.stream().map(OrderItemEntity::getPdName).toArray(), ";");
                // 限120个字以内
                titles = titles.substring(0, Math.min(120, titles.length()));
            }

            UploadShippingInfoReq req = new UploadShippingInfoReq();
            OrderKey orderKey = new OrderKey();
            orderKey.setOrder_number_type(2);
            orderKey.setTransaction_id(transactionNumber);
            req.setOrder_key(orderKey);
            req.setLogistics_type(2);
            req.setDelivery_mode(1);
            Shipping shipping = new Shipping();
            shipping.setItem_desc(titles);
            req.setShipping_list(Lists.newArrayList(shipping));
            req.setUpload_time(ZonedDateTime.now().format(DatePattern.createFormatter(DatePattern.UTC_MS_WITH_XXX_OFFSET_PATTERN)));
            req.setPayer(new Payer(account.getOpenId()));
            UploadShippingInfoResult result = this.uploadShippingInfo(accessToken, req);
            if (result == null || !result.successed()) {
                log.error("【小程序发货信息管理】【数据异常】微信上传发货信息错误，transactionNumber：{}，result={}",transactionNumber,  result);
                return;
            }

            // 保存上传成功的订单，避免重复上传
            this.saveWxShippingInfoUploadRecord(transactionNumber, orderNo);
        } catch (Exception e) {
            log.error("微信上传发货信息错误, transactionNumber={}, orderNo ={}", transactionNumber, orderNo, e);
        }
    }

    private void saveWxShippingInfoUploadRecord(String transactionNumber, String orderNo) {
        log.info("开始保存以上传的订单信息.transactionNumber:{}, order:{}", transactionNumber, orderNo);
        // 上传发货信息接口成功，插入日志记录，用来避免重复上传
        WxShippingInfoUploadRecordCommandParam saveRecord = new WxShippingInfoUploadRecordCommandParam();
        saveRecord.setMasterOrderNo(orderNo);
        saveRecord.setTransactionId(transactionNumber);
        saveRecord.setStatus(1);
        saveRecord.setCreateTime(LocalDateTime.now());
        saveRecord.setUpdateTime(LocalDateTime.now());
        wxShippingInfoUploadRecordCommandRepository.insertSelective(saveRecord);
    }


    /**
     * 发货信息录入接口
     *
     * @param access_token
     * @param req
     * @return
     */
    public UploadShippingInfoResult uploadShippingInfo(String access_token, UploadShippingInfoReq req) {
        String url = String.format(ApiConsts.UPLOAD_SHIPPING_INFO, access_token);
        log.info("开始上传订单信息：url:{}, body:{}", url, JSON.toJSONString(req));
        String resp = cn.hutool.http.HttpUtil.post(url, JSONUtil.toJsonStr(req), 2000);
        log.info("上传订单信息结束： body {}", JSON.toJSONString(resp));
        return JSONObject.parseObject(resp, UploadShippingInfoResult.class);
    }


    /**
     * 根据主单上传订单信息
     *
     * @param masterOrderNo
     */
    public void uploadShippingInfoByOrderNo(String orderNo, Integer orderType) {
        log.info("【小程序发货信息管理】开始根据订单信息上传数据。orderNo：{}", orderNo);
        if(StringUtils.isBlank(orderNo)) {
            log.warn("【小程序发货信息管理】【数据异常】：订单信息为空！");
            return;
        }

        PaymentQueryParam param = new PaymentQueryParam();
        param.setOrderNo(orderNo);
        param.setStatus(PaymentEnums.PaymentStatus.SUCCESS.getStatus());
        List<PaymentEntity> paymentEntities = paymentQueryRepository.selectByCondition(param);

        if(CollectionUtil.isEmpty(paymentEntities)) {
            log.error("【小程序发货信息管理】【数据异常】：订单对应的支付信息不存在！", new BizException("订单对应的支付信息不存在"));
            return;
        }
        PaymentEntity paymentEntity = paymentEntities.get(0);
        boolean checkFlag = checkPaymentForShipping(paymentEntity);
        if (!checkFlag) {
            return;
        }
        uploadShippingInfoCommon(paymentEntity.getTransactionNumber(), paymentEntity.getOrderNo(), orderType);
    }

    private boolean checkPaymentForShipping(PaymentEntity payment) {
        // 非微信支付
        if (!checkPaymentType(payment)) {
            return false;
        }

        // 非支付成功
        if (!PaymentEnums.PaymentStatus.SUCCESS.getStatus().equals(payment.getStatus())) {
            log.warn("【小程序发货信息管理】【异常】非支付成功的订单不做处理。payment：{}", JSON.toJSONString(payment));
            return false;
        }
        return true;
    }

    private boolean checkPaymentType(PaymentEntity payment) {
        if (PaymentConsts.WX_MINI_PROGRAM_PAYMENT_TYPE.equals(payment.getPayType())) {
            return true;
        }
        if (PaymentConsts.DIN_PAY.equals(payment.getPayType()) && PaymentConsts.DIN_MP_PAY_TYPE.equals(payment.getBocPayType())) {
            return true;
        }
        log.warn("【小程序发货信息管理】【异常】非微信小程序的订单不做处理。payment：{}", JSON.toJSONString(payment));
        return false;
    }
}
