package net.summerfarm.manage.application.inbound.controller.marketItem.input.query;

import lombok.Data;
import java.io.Serializable;
import net.xianmu.common.input.BasePageInput;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2025-07-03 16:33:54
 * @version 1.0
 *
 */
@Data
public class MarketItemAiExtQueryInput extends BasePageInput implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 租户id
	 */
	private Long tenantId;

	/**
	 * SKU编码, inventory.sku
	 */
	private String sku;

	/**
	 * SPUID, products.pd_id
	 */
	private Long pdId;

	/**
	 * AI扩展类型，1-商品相关问题
	 */
	private Integer extType;

	/**
	 * AI扩展值
	 */
	private String extValue;



}