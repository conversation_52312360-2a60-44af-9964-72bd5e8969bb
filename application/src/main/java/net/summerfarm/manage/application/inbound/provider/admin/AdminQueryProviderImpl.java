package net.summerfarm.manage.application.inbound.provider.admin;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.provider.admin.AdminQueryProvider;
import net.summerfarm.client.req.admin.AdminQueryReq;
import net.summerfarm.client.resp.admin.AdminResp;
import net.summerfarm.manage.domain.admin.entity.AdminEntity;
import net.summerfarm.manage.domain.admin.repository.AdminQueryRepository;
import net.summerfarm.manage.domain.crm.entity.FollowUpRelationEntity;
import net.summerfarm.manage.domain.crm.repository.FollowUpRelationRepository;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@Component
public class AdminQueryProviderImpl implements AdminQueryProvider {
    @Resource
    AdminQueryRepository adminQueryRepository;

    @Resource
    private FollowUpRelationRepository followUpRelationRepository;

    @Override
    public DubboResponse<List<AdminResp>> batchQueryAdmins(AdminQueryReq adminQueryReq) {
        if (CollectionUtil.isEmpty(adminQueryReq.getRealNames()) && CollectionUtil.isEmpty(adminQueryReq.getAdminIds())){
            return DubboResponse.getOK(new ArrayList<>());
        }
        List<AdminEntity> adminEntities = (!CollectionUtil.isEmpty(adminQueryReq.getRealNames()))? adminQueryRepository.selectByRealNames(adminQueryReq.getRealNames()):
                adminQueryRepository.selectByAdminIds(adminQueryReq.getAdminIds().stream().map(Long::valueOf).collect(Collectors.toList()));
        List<AdminResp> collect = adminEntities.stream().map(AdminQueryProviderImpl::convert).collect(Collectors.toList());
        return DubboResponse.getOK(collect);
    }

    @Override
    public DubboResponse<AdminResp> queryByAdminId(Long adminId) {
        AdminEntity adminEntity = adminQueryRepository.selectByPrimaryKey(adminId);
        return DubboResponse.getOK(AdminQueryProviderImpl.convert(adminEntity));
    }

    @Override
    public DubboResponse<List<AdminResp>> queryByAdminIdList(List<Long> adminId) {
        List<AdminEntity> adminEntities = adminQueryRepository.selectByAdminIds(adminId);
        return DubboResponse.getOK(adminEntities.stream().map(AdminQueryProviderImpl::convert).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<AdminResp> queryByMId(Long mId) {
        if (null == mId){
            return DubboResponse.getDefaultError("mId为空");
        }
        List<FollowUpRelationEntity> followUpRelationEntities = followUpRelationRepository.batchQueryByMids(Collections.singletonList(mId));
        if (CollectionUtil.isEmpty(followUpRelationEntities)){
            return DubboResponse.getOK(null);
        }
        FollowUpRelationEntity followUpRelationEntity = followUpRelationEntities.get(0);
        if (null == followUpRelationEntity){
            return DubboResponse.getOK(null);
        }
        return this.queryByAdminId(followUpRelationEntity.getAdminId().longValue());
    }


    private  static AdminResp convert(AdminEntity admin){
        AdminResp resp = new AdminResp();
        resp.setAdminId(admin.getAdminId().intValue());
        resp.setNameRemark(admin.getNameRemakes());
        resp.setRealName(admin.getRealname());
        resp.setBaseUserId(admin.getBaseUserId());
        resp.setUsername(admin.getUsername());
        resp.setSalerName(admin.getSalerName());
        resp.setBillToPay(admin.getBillToPay());
        if (admin.getSalerId()!=null){
            resp.setSalerId(admin.getSalerId().longValue());
        }
        resp.setSalerName(admin.getSalerName());
        resp.setPhone(admin.getPhone());
        return resp;
    }
}
