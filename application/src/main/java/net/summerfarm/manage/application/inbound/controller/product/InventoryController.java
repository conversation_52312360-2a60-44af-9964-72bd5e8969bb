package net.summerfarm.manage.application.inbound.controller.product;


import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.product.input.*;
import net.summerfarm.manage.application.inbound.controller.product.input.command.SkuBaseInfoCommandInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.*;
import net.summerfarm.manage.application.service.product.mall.InventoryCommandService;
import net.summerfarm.manage.application.service.product.mall.InventoryQueryService;
import net.summerfarm.manage.application.service.product.mall.ProductQueryService;
import net.summerfarm.manage.common.config.NacosPropertiesHolder;
import net.summerfarm.manage.common.constants.Global;
import net.xianmu.common.exception.ExceptionUtil;
import net.xianmu.common.result.CommonResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName InventoryController
 * @Description
 * <AUTHOR>
 * @Date 16:03 2024/4/30
 * @Version 1.0
 **/
@RestController
@RequestMapping("/inventory")
public class InventoryController {

    @Resource
    private InventoryQueryService inventoryQueryService;

    @Resource
    private InventoryCommandService inventoryCommandService;

    @Resource
    private ProductQueryService productQueryService;

    @Resource
    private NacosPropertiesHolder nacosPropertiesHolder;


    /**
     * 商品校验-spu、sku编辑
     *
     * @param input
     */
    @RequestMapping(value = "/vaild", method = RequestMethod.GET)
    @RequiresPermissions(value = {"inventory:update", Global.SA}, logical = Logical.OR)
    public CommonResult<Boolean> vaild(InventoryVaildInput input) {
        inventoryQueryService.vaild (input);
        return CommonResult.ok ();
    }

    /**
     * 商品编辑
     *
     * @param input
     */
    @RequestMapping(value = "/upset/sku", method = RequestMethod.PUT)
    @RequiresPermissions(value = {"inventory:update", Global.SA}, logical = Logical.OR)
    public CommonResult<Boolean> updateSkuInfo(@RequestBody @Valid InventoryUpdateInput input) {
        inventoryCommandService.updateSkuInfo (input);
        return CommonResult.ok ();
    }

    /**
     * 商品视频编辑
     *
     * @param input
     */
    @RequestMapping(value = "/upset/skuVideo", method = RequestMethod.PUT)
    @RequiresPermissions(value = {"skuVideo:update", Global.SA}, logical = Logical.OR)
    public CommonResult<Boolean> updateSkuVideo(@RequestBody @Valid InventoryVideoInput input) {
        inventoryCommandService.updateSkuVideo (input);
        return CommonResult.ok ();
    }


    /**
     * 商品标签列表
     *
     * @param
     */
    @RequestMapping(value = "/query/all-item-label", method = RequestMethod.POST)
    @RequiresPermissions(value = {"inventory:update", Global.SA}, logical = Logical.OR)
    public CommonResult<List<ItemLabelVO>> allItemLabel(@RequestBody ItemLabelQueryInput input) {
        return CommonResult.ok (inventoryCommandService.allItemLabel (input));
    }

    /**
     * 商品标签新增
     *
     * @param
     */
    @RequestMapping(value = "/upset/insert-item-label", method = RequestMethod.POST)
    @RequiresPermissions(value = {"inventory:update", Global.SA}, logical = Logical.OR)
    public CommonResult<ItemLabelVO> insertItemLabel(@RequestBody @Valid ItemLabelInput input) {
        return CommonResult.ok (inventoryCommandService.insertItemLabel (input));
    }

    /**
     * 查询sku vo列表
     * 视频上传 列表使用，只展示pop
     * @param input
     */
    @RequestMapping("/query/page/sku")
    @RequiresPermissions(value = {"skuVideo:select", Global.SA},logical = Logical.OR)
    public CommonResult<PageInfo<MarketItemListVO>> pageSku(@RequestBody MarketBaseQueryInput input) {
        PageInfo<MarketItemListVO> marketItemListVOPageInfo = inventoryCommandService.pageSku (input);
        return CommonResult.ok (marketItemListVOPageInfo);
    }

    /**
     * 查询sku基础信息
     *
     * <AUTHOR>
     * @date 2025/1/14 15:24
     */
    @RequestMapping("/query/list/base-sku")
    public CommonResult<List<InventoryBaseVO>> listSkuBaseInfo(@RequestBody InventoryBaseQueryInput input) {
        if (Objects.isNull(input.getSku()) && CollectionUtils.isEmpty(input.getSkuList())) {
            return CommonResult.ok(Lists.newArrayList());
        }
        List<InventoryBaseVO> inventoryBaseVOList = inventoryQueryService.listSkuBaseInfo(input);
        return CommonResult.ok(inventoryBaseVOList);
    }


    /**
     * 查询运营大区售卖列表 - 大客户报价选品
     * @param input
     */
    @RequestMapping("/sku/page-by-large-area")
    public CommonResult<PageInfo<MarketItemByLargeAreaListVO>> pageSkuByLargeArea(@RequestBody @Validated SkuByLargeAreaQueryInput input) {
        PageInfo<MarketItemByLargeAreaListVO> marketItemListVOPageInfo = inventoryQueryService.pageSkuByLargeArea (input);
        return CommonResult.ok (marketItemListVOPageInfo);
    }

    /**
     * sku分页搜索
     *
     * <AUTHOR>
     * @date 2025/3/27 19:18
     */
    @RequestMapping("/sku/page")
    public CommonResult<PageInfo<InventoryBaseVO>> pageQueryInventory(@RequestBody InventoryBaseQueryInput input) {
        ExceptionUtil.Params.checkAndThrow(Objects.nonNull(input.getPageIndex())
                && Objects.nonNull(input.getPageSize()), "分页参数缺失");
        PageInfo<InventoryBaseVO> pageInfo = inventoryQueryService.pageQueryInventory(input);
        return CommonResult.ok(pageInfo);
    }

    /**
     * sku基础信息编辑
     *
     * @param input
     */
    @RequestMapping(value = "/upsert/base-sku", method = RequestMethod.POST)
    public CommonResult<Boolean> updateSkuBaseInfo(@RequestBody @Valid SkuBaseInfoCommandInput input) {
        inventoryCommandService.updateSkuBaseInfo(input);
        return CommonResult.ok ();
    }

    /**
     * pop商品关系-待引用鲜沐商品列表
     * 等待被关联的
     * <AUTHOR>
     * @date 2025/4/3 16:06
     */
    @RequestMapping("/query/pagePendingAssociationProduct")
    public CommonResult<PageInfo<PendingAssociationProductVO>> pagePendingAssociationProduct(@RequestBody @Validated PendingAssociationProductQueryInput input) {
        input.setWarehouseNo(nacosPropertiesHolder.getDefaultXmToPopWarehouseNo());
        return CommonResult.ok (inventoryQueryService.pagePendingAssociationProduct(input));
    }

    /**
     * pop商品关系-待引用鲜沐商品类目列表
     *
     * <AUTHOR>
     * @date 2025/4/3 16:06
     */
    @RequestMapping("/query/listPendingAssociationCategory")
    public CommonResult<List<BackCategoryVO>> listPendingAssociationCategory(@RequestBody @Validated PendingAssociationCategoryQueryInput input) {
        input.setWarehouseNo(nacosPropertiesHolder.getDefaultXmToPopWarehouseNo());
        return CommonResult.ok (inventoryQueryService.listPendingAssociationCategory(input));
    }

}
