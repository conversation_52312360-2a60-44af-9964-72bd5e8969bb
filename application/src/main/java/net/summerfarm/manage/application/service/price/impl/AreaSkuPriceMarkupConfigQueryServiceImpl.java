package net.summerfarm.manage.application.service.price.impl;


import cn.hutool.core.collection.CollUtil;
import net.summerfarm.manage.application.service.price.AreaSkuPriceMarkupConfigQueryService;
import net.summerfarm.manage.domain.price.repository.AreaSkuPriceMarkupConfigQueryRepository;
import net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity;
import net.summerfarm.manage.domain.price.param.query.AreaSkuPriceMarkupConfigQueryParam;
import net.summerfarm.manage.application.inbound.controller.price.input.query.AreaSkuPriceMarkupConfigQueryInput;
import net.summerfarm.manage.application.inbound.controller.price.assembler.AreaSkuPriceMarkupConfigAssembler;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.domain.product.repository.CategoryQueryRepository;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
*
* <AUTHOR>
* @date 2025-03-26 13:59:07
* @version 1.0
*
*/
@Service
public class AreaSkuPriceMarkupConfigQueryServiceImpl implements AreaSkuPriceMarkupConfigQueryService {

    @Autowired
    private AreaSkuPriceMarkupConfigQueryRepository areaSkuPriceMarkupConfigQueryRepository;

    @Autowired
    private CategoryQueryRepository categoryQueryRepository;

    @Override
    public PageInfo<AreaSkuPriceMarkupConfigEntity> getPage(AreaSkuPriceMarkupConfigQueryInput input) {
        AreaSkuPriceMarkupConfigQueryParam queryParam = AreaSkuPriceMarkupConfigAssembler.INSTANCE.toAreaSkuPriceMarkupConfigQueryParam(input);

        this.transFrontToCategoryIds(queryParam);
        return areaSkuPriceMarkupConfigQueryRepository.getPage(queryParam);
    }

    private void transFrontToCategoryIds(AreaSkuPriceMarkupConfigQueryParam queryParam){
        List<Long> frontCategoryIds = queryParam.getFrontCategoryIds();
        if(CollUtil.isEmpty(frontCategoryIds)) {
            return;
        }
        Long frontId = frontCategoryIds.get(frontCategoryIds.size() - 1);
        List<Long> categoryIds = new ArrayList<>();
        categoryIds.add(0L);
        categoryIds.addAll(categoryQueryRepository.selectCategoryIdsByFrontId(frontId));
        queryParam.setCategoryIds(categoryIds);
    }

    @Override
    public AreaSkuPriceMarkupConfigEntity getDetail(Long id){
        if (Objects.isNull(id)) {
            throw new BizException("请求参数为空！");
        }
        return areaSkuPriceMarkupConfigQueryRepository.selectById(id);
    }
}