package net.summerfarm.manage.application.service.payment.impl;

import net.summerfarm.manage.application.inbound.converter.payment.PaymentConverter;
import net.summerfarm.manage.application.service.payment.PaymentRoutingCommandService;
import net.summerfarm.payment.routing.model.dto.PaymentChannelSaveDTO;
import net.summerfarm.payment.routing.model.dto.PaymentChannelStatusDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRuleSaveDTO;
import net.summerfarm.payment.routing.model.dto.PaymentRuleRoutingUpdateDTO;
import net.summerfarm.payment.routing.model.params.PaymentChannelSaveParams;
import net.summerfarm.payment.routing.model.params.PaymentChannelStatusParams;
import net.summerfarm.payment.routing.model.params.PaymentRuleSaveParams;
import net.summerfarm.payment.routing.model.params.PaymentRuleRoutingUpdateParams;
import net.summerfarm.payment.routing.service.PaymentChannelService;
import net.summerfarm.payment.routing.service.PaymentRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 支付路由命令服务实现
 * <AUTHOR>
 */
@Service
public class PaymentRoutingCommandServiceImpl implements PaymentRoutingCommandService {

    @Resource
    private PaymentChannelService paymentChannelService;

    @Resource
    private PaymentRuleService paymentRuleService;

    @Override
    public Long saveChannel(PaymentChannelSaveParams params) {
        PaymentChannelSaveDTO saveDTO = convertToPaymentChannelSaveDTO(params);
        return paymentChannelService.saveChannel(saveDTO);
    }

    @Override
    public Boolean changeStatus(PaymentChannelStatusParams params) {
        PaymentChannelStatusDTO statusDTO = convertToPaymentChannelStatusDTO(params);
        return paymentChannelService.changeStatus(statusDTO);
    }

    @Override
    public Long saveRule(PaymentRuleSaveParams params) {
        PaymentRuleSaveDTO saveDTO = convertToPaymentRuleSaveDTO(params);
        return paymentRuleService.saveRule(saveDTO);
    }

    @Override
    public Boolean bindRuleRouting(PaymentRuleRoutingUpdateParams params) {
        PaymentRuleRoutingUpdateDTO updateDTO = convertToPaymentRuleRoutingUpdateDTO(params);
        return paymentRuleService.bindRuleRouting(updateDTO);
    }

    private PaymentChannelSaveDTO convertToPaymentChannelSaveDTO(PaymentChannelSaveParams params) {
        return PaymentConverter.convert2SaveDTO(params);
    }

    private PaymentChannelStatusDTO convertToPaymentChannelStatusDTO(PaymentChannelStatusParams params) {
        return PaymentConverter.convert2ChannelStatusDTO(params);
    }

    private PaymentRuleSaveDTO convertToPaymentRuleSaveDTO(PaymentRuleSaveParams params) {
        return PaymentConverter.convert2RuleSaveDTO(params);
    }

    private PaymentRuleRoutingUpdateDTO convertToPaymentRuleRoutingUpdateDTO(PaymentRuleRoutingUpdateParams params) {
        return PaymentConverter.convert2RuleRoutingUpdateDTO(params);
    }
}
