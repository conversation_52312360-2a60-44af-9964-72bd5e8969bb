package net.summerfarm.manage.application.inbound.controller.dts;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.rocketmq.RocketMqMessageConstant;
import net.summerfarm.manage.application.service.dts.DbTableDml;
import net.summerfarm.manage.application.service.dts.DbTableDmlFactory;
import net.summerfarm.manage.application.service.dts.DbTableOrderlyDmlFactory;
import net.summerfarm.manage.common.dto.DtsModel;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @descripton binlog顺序消费
 * @date 2024/4/26 16:20
 */
@Component
@Slf4j
@MqOrderlyListener(topic = "mysql-binlog-orderly",tag = "delivery_plan", consumerGroup = "GID_sf_mall_manage_binlog_orderly", maxReconsumeTimes = 2,  maxReconsumeTimesWarnLog = 2)
public class BinlogOrderlyListener extends AbstractMqListener<DtsModel> {

    @Resource
    private DbTableOrderlyDmlFactory dbTableOrderlyDmlFactory;

    @Override
    public void process(DtsModel dtsModel) {
        log.info("rocketmq 收到顺序消息，事件类型：{}，recordId/msg-key：{}， 表：{}.{}",
                dtsModel.getType(), dtsModel.getMsgKey(), dtsModel.getDatabase(), dtsModel.getTable());

        DbTableDml creator = dbTableOrderlyDmlFactory.creator(dtsModel.getTable());
        if (Objects.nonNull(creator)) {
            creator.handle(dtsModel);
        } else {
            log.info("未在DbTableDmlFactory注册的table:{},请先注册后再做处理!", dtsModel.getTable());
        }
    }
}
