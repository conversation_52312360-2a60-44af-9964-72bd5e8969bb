package net.summerfarm.manage.application.inbound.controller.job.input.command;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-12-18 15:57:19
 * @version 1.0
 *
 */
@Data
public class CrmJobMerchantDetailCommandInput implements Serializable{
	/**
	 * primary key
	 */
	private Long id;

	/**
	 * create time
	 */
	private LocalDateTime createTime;

	/**
	 * update time
	 */
	private LocalDateTime updateTime;

	/**
	 * 任务Id FK crm_job
	 */
	private Long jobId;

	/**
	 * 门店id
	 */
	private Long mId;

	/**
	 * 完成状态 0.未完成 1.已完成
	 */
	private Integer status;

	/**
	 * 门店命中品列表
	 */
	private String merchantProductList;

	/**
	 * 拜访记录id
	 */
	private Integer followUpRecordId;

	/**
	 * 门店命中商品数
	 */
	private Integer merchantProductCnt;

	/**
	 * 任务领取状态：0-已领取，1-未领取
	 */
	private Integer claimingStatus;

	/**
	 * 任务领取时间
	 */
	private LocalDateTime claimingTime;

	/**
	 * 任务完成时间
	 */
	private LocalDateTime completeTime;

	/**
	 * 完成任务的订单号列表
	 */
	private String orderNo;

	/**
	 * 类目id列表（应用于下单任务）
	 */
	private String categoryIdList;



}