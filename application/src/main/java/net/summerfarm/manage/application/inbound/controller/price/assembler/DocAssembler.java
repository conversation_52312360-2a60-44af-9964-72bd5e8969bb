package net.summerfarm.manage.application.inbound.controller.price.assembler;

import net.summerfarm.manage.application.inbound.controller.price.vo.DocInfoVO;
import net.summerfarm.manage.common.enums.price.StockWarningLabelEnum;
import net.summerfarm.manage.facade.scp.dto.DocInfoDTO;

public class DocAssembler {
    public static DocInfoVO convertVO(DocInfoDTO docInfoDTO, StockWarningLabelEnum stockWarningLabelEnum) {
        DocInfoVO vo = new DocInfoVO();
        if (docInfoDTO != null) {
            vo.setDoc(docInfoDTO.getDoc());
            vo.setLevelLabel(docInfoDTO.getLevelLabel());
            vo.setStockLevelMinimumDay(docInfoDTO.getStockLevelMinimumDay());
            vo.setStockLevelTargetDay(docInfoDTO.getStockLevelTargetDay());
            vo.setStockLevelMaximumDay(docInfoDTO.getStockLevelMaximumDay());
            vo.setSalesQuantity(docInfoDTO.getSalesQuantity());
            vo.setInitStockQuantity(docInfoDTO.getInitStockQuantity());
            vo.setStockWarmingLabelValue(stockWarningLabelEnum != null ? stockWarningLabelEnum.getValue() : null);
            vo.setStockWarmingLabelContent(stockWarningLabelEnum != null ? stockWarningLabelEnum.getContent() : null);
        }
        return vo;
    }
}
