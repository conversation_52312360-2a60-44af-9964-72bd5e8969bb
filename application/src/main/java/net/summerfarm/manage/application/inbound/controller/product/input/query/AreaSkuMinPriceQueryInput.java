package net.summerfarm.manage.application.inbound.controller.product.input.query;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/16 14:44
 * @PackageName:net.summerfarm.manage.application.inbound.controller.product.input.query
 * @ClassName: AreaSkuMinPriceQueryInput
 * @Description: TODO
 * @Version 1.0
 */
@Data
public class AreaSkuMinPriceQueryInput implements Serializable {

    /**
     * sku
     */
    @NotNull(message = "sku不能为空")
    private String sku;

    /**
     * 仓库编号
     */
    @NotNull(message = "仓库编号不能为空")
    private Integer warehouseNo;

    /**
     * 供应商id
     */
    @NotNull(message = "供应商id不能为空")
    private Long supplierId;
}
