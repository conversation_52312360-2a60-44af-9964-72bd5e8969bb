package net.summerfarm.manage.application.inbound.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.merchant.MerchantFrequentlyBuyingSkuNotificationConfigQueryService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/5/22 14:00
 * @PackageName:net.xianmu.marketing.center.application.inbound.schedule
 * @ClassName: ActivityNotificationProcessor
 * @Description: TODO
 * @Version 1.0
 */
@Component
@Slf4j
public class ActivityNotificationProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private MerchantFrequentlyBuyingSkuNotificationConfigQueryService notificationConfigQueryService;

    private static final String ACTIVITY_ID_KEY = "activityId";

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("门店采购助手-特价活动提醒通知start:{}", LocalDateTime.now());
        Long activityId = null;
        if (StringUtils.isNotBlank(context.getInstanceParameters())) {
            JSONObject jsonObject = JSON.parseObject(context.getInstanceParameters());
            if (jsonObject.containsKey(ACTIVITY_ID_KEY)) {
                activityId = Long.valueOf(jsonObject.getString(ACTIVITY_ID_KEY));
            }
        }
        notificationConfigQueryService.notificationToMerchant(activityId);
        log.info("门店采购助手-特价活动提醒通知end:{}", LocalDateTime.now());
        return new ProcessResult(true);
    }
}
