package net.summerfarm.manage.application.inbound.controller.merchant.assembler;


import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantAccountTransferVO;
import net.summerfarm.manage.application.inbound.controller.merchant.vo.MerchantTransferCheckVO;
import net.summerfarm.manage.domain.merchant.entity.MerchantAccountTransferEntity;
import net.summerfarm.manage.application.inbound.controller.merchant.input.command.MerchantAccountTransferCommandInput;
import net.summerfarm.manage.application.inbound.controller.merchant.input.query.MerchantAccountTransferQueryInput;
import net.summerfarm.manage.domain.merchant.entity.MerchantEntity;
import net.summerfarm.manage.domain.merchant.param.query.MerchantAccountTransferQueryParam;
import net.summerfarm.manage.domain.merchant.param.command.MerchantAccountTransferCommandParam;

import java.util.ArrayList;
import java.util.List;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-01-10 14:07:22
 * @version 1.0
 *
 */
public class MerchantAccountTransferAssembler {

    private MerchantAccountTransferAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static MerchantAccountTransferQueryParam toMerchantAccountTransferQueryParam(MerchantAccountTransferQueryInput merchantAccountTransferQueryInput) {

        if (merchantAccountTransferQueryInput == null) {
            return null;
        }
        MerchantAccountTransferQueryParam merchantAccountTransferQueryParam = new MerchantAccountTransferQueryParam();
        merchantAccountTransferQueryParam.setMName(merchantAccountTransferQueryInput.getMName());
        merchantAccountTransferQueryParam.setMId(merchantAccountTransferQueryInput.getMId());
        merchantAccountTransferQueryParam.setTransferMids(merchantAccountTransferQueryInput.getTransferMids());
        merchantAccountTransferQueryParam.setId(merchantAccountTransferQueryInput.getId());
        merchantAccountTransferQueryParam.setPageIndex(merchantAccountTransferQueryInput.getPageIndex());
        merchantAccountTransferQueryParam.setPageSize(merchantAccountTransferQueryInput.getPageSize());
        merchantAccountTransferQueryParam.setSortList(merchantAccountTransferQueryInput.getSortList());
        return merchantAccountTransferQueryParam;
    }






// ------------------------------- response ----------------------------

    public static List<MerchantAccountTransferVO> toMerchantAccountTransferVOList(List<MerchantAccountTransferEntity> merchantAccountTransferEntityList) {
        if (merchantAccountTransferEntityList == null) {
            return Collections.emptyList();
        }
        List<MerchantAccountTransferVO> merchantAccountTransferVOList = new ArrayList<>();
        for (MerchantAccountTransferEntity merchantAccountTransferEntity : merchantAccountTransferEntityList) {
            merchantAccountTransferVOList.add(toMerchantAccountTransferVO(merchantAccountTransferEntity));
        }
        return merchantAccountTransferVOList;
}


   public static MerchantAccountTransferVO toMerchantAccountTransferVO(MerchantAccountTransferEntity merchantAccountTransferEntity) {
       if (merchantAccountTransferEntity == null) {
            return null;
       }
       MerchantAccountTransferVO merchantAccountTransferVO = new MerchantAccountTransferVO();
       merchantAccountTransferVO.setId(merchantAccountTransferEntity.getId());
       merchantAccountTransferVO.setCreateTime(merchantAccountTransferEntity.getCreateTime());
       merchantAccountTransferVO.setUpdateTime(merchantAccountTransferEntity.getUpdateTime());
       merchantAccountTransferVO.setMId(merchantAccountTransferEntity.getMId());
       merchantAccountTransferVO.setMname(merchantAccountTransferEntity.getMname());
       merchantAccountTransferVO.setTransferMId(merchantAccountTransferEntity.getTransferMId());
       merchantAccountTransferVO.setOperatorName(merchantAccountTransferEntity.getOperatorName());
       merchantAccountTransferVO.setAreaNo(merchantAccountTransferEntity.getAreaNo());
       merchantAccountTransferVO.setAreaName(merchantAccountTransferEntity.getAreaName());
       merchantAccountTransferVO.setRemark(merchantAccountTransferEntity.getRemark());
       merchantAccountTransferVO.setAddr(merchantAccountTransferEntity.getAddr());
       merchantAccountTransferVO.setBdName(merchantAccountTransferEntity.getBdName());
       merchantAccountTransferVO.setTransferMname(merchantAccountTransferEntity.getTransferMname());
       merchantAccountTransferVO.setTransferBdName(merchantAccountTransferEntity.getTransferBdName());
       merchantAccountTransferVO.setPhone(merchantAccountTransferEntity.getPhone());
       merchantAccountTransferVO.setTransferPhone(merchantAccountTransferEntity.getTransferPhone());
       return merchantAccountTransferVO;
   }


    public static MerchantTransferCheckVO toMerchantTransferCheckVO(MerchantEntity merchantEntity) {

        if (merchantEntity == null) {
            return null;
        }
        MerchantTransferCheckVO merchantTransferCheckVO = new MerchantTransferCheckVO();
        merchantTransferCheckVO.setMId(merchantEntity.getMId());
        merchantTransferCheckVO.setMname(merchantEntity.getMname());
        merchantTransferCheckVO.setMcontact(merchantEntity.getMcontact());
        merchantTransferCheckVO.setPhone(merchantEntity.getPhone());
        merchantTransferCheckVO.setIslock(merchantEntity.getIslock());
        merchantTransferCheckVO.setProvince(merchantEntity.getProvince());
        merchantTransferCheckVO.setCity(merchantEntity.getCity());
        merchantTransferCheckVO.setArea(merchantEntity.getArea());
        merchantTransferCheckVO.setAddress(merchantEntity.getAddress());
        merchantTransferCheckVO.setAreaNo(merchantEntity.getAreaNo());
        merchantTransferCheckVO.setSize(merchantEntity.getSize());
        return merchantTransferCheckVO;
    }

}
