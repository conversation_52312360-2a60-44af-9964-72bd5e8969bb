package net.summerfarm.manage.application.inbound.controller.product.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description
 * @Date 2025/4/3 16:26
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PendingAssociationCategoryQueryInput {

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 库存仓编号
     */
    private Integer warehouseNo;

}
