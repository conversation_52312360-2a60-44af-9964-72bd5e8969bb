package net.summerfarm.manage.application.inbound.controller.product;

import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.product.assembler.AppPopBiaoguoProductsDfAssembler;
import net.summerfarm.manage.application.service.product.mall.AppPopBiaoguoProductsDfQueryService;
import net.summerfarm.manage.domain.product.entity.AppPopBiaoguoProductsDfEntity;
import net.summerfarm.manage.application.inbound.controller.product.vo.AppPopBiaoguoProductsDfVO;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.xianmu.common.exception.ExceptionUtil;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import net.summerfarm.manage.application.inbound.controller.product.input.AppPopBiaoguoProductsDfQueryInput;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;


/**
 * @Title POP标果商品详细信息明细表
 * @Description POP标果商品详细信息明细表功能模块
 * <AUTHOR>
 * @date 2024-12-12 11:19:19
 * @version 1.0
 */
@RestController
@RequestMapping(value="/appPopBiaoguoProductsDf")
public class AppPopBiaoguoProductsDfController{

	@Resource
	private AppPopBiaoguoProductsDfQueryService appPopBiaoguoProductsDfQueryService;


	/**
	 * POP标果商品详细信息明细表列表
	 * @return AppPopBiaoguoProductsDfVO
	 */
	@PostMapping(value="/query/page")
	public CommonResult<PageInfo<AppPopBiaoguoProductsDfVO>> getPage(@RequestBody AppPopBiaoguoProductsDfQueryInput input){
		ExceptionUtil.Params.checkAndThrow(Objects.nonNull(input.getPageIndex())
				&& Objects.nonNull(input.getPageSize()), "分页参数不能为空");
		input.setDs(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd")));
		PageInfo<AppPopBiaoguoProductsDfEntity> page = appPopBiaoguoProductsDfQueryService.getPage(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, AppPopBiaoguoProductsDfAssembler::toAppPopBiaoguoProductsDfVO));
	}

	/**
	* 获取详情
	* @return AppPopBiaoguoProductsDfVO
	*/
	@PostMapping(value = "/query/detail")
	public CommonResult<AppPopBiaoguoProductsDfVO> detail(@RequestBody AppPopBiaoguoProductsDfQueryInput input){
		if(input == null || input.getId() == null){
			throw new ParamsException("请求参数缺失:缺少id字段");
		}
		return CommonResult.ok(AppPopBiaoguoProductsDfAssembler.toAppPopBiaoguoProductsDfVO(appPopBiaoguoProductsDfQueryService.getDetail(input.getId())));
	}




}

