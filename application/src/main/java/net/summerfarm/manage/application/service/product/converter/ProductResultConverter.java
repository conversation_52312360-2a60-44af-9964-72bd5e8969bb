package net.summerfarm.manage.application.service.product.converter;

import net.summerfarm.manage.application.inbound.controller.product.vo.InventoryDetailVO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import net.summerfarm.manage.application.inbound.controller.product.vo.MarketItemAggListVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.ProductVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.ProductsPropertyValueVO;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;
import net.summerfarm.manage.domain.product.entity.MarketItemAggListEntity;
import net.summerfarm.manage.domain.product.entity.ProductEntity;
import net.summerfarm.manage.domain.product.entity.ProductsEntity;
import net.summerfarm.manage.domain.product.entity.ProductsPropertyValueEntity;
import org.springframework.util.CollectionUtils;


/**
 * @author: <EMAIL>
 * @create: 2024/1/26
 */
public class ProductResultConverter {


    private ProductResultConverter() {
        // 无需实现
    }

    public static List<ProductsPropertyValueVO> toProductsPropertyValueVOList(List<ProductsPropertyValueEntity> productsPropertyValueEntityList) {
        if (productsPropertyValueEntityList == null) {
            return Collections.emptyList();
        }
        List<ProductsPropertyValueVO> productsPropertyValueVOList = new ArrayList<>();
        for (ProductsPropertyValueEntity productsPropertyValueEntity : productsPropertyValueEntityList) {
            productsPropertyValueVOList.add(toProductsPropertyValueVO(productsPropertyValueEntity));
        }
        return productsPropertyValueVOList;
    }

    public static ProductsPropertyValueVO toProductsPropertyValueVO(ProductsPropertyValueEntity productsPropertyValueEntity) {
        if (productsPropertyValueEntity == null) {
            return null;
        }
        ProductsPropertyValueVO productsPropertyValueVO = new ProductsPropertyValueVO();
        productsPropertyValueVO.setId(productsPropertyValueEntity.getId());
        productsPropertyValueVO.setPdId(productsPropertyValueEntity.getPdId());
        productsPropertyValueVO.setProductsPropertyId(productsPropertyValueEntity.getProductsPropertyId());
        productsPropertyValueVO.setProductsPropertyValue(productsPropertyValueEntity.getProductsPropertyValue());
        productsPropertyValueVO.setName(productsPropertyValueEntity.getName());
        return productsPropertyValueVO;
    }

    public static List<ProductVO> toProductVOList(List<ProductEntity> productEntityList) {
        if (productEntityList == null) {
            return Collections.emptyList();
        }
        List<ProductVO> productVOList = new ArrayList<>();
        for (ProductEntity productEntity : productEntityList) {
            productVOList.add(toProductVO(productEntity));
        }
        return productVOList;
    }

    public static ProductVO toProductVO(ProductEntity productEntity) {
        if (productEntity == null) {
            return null;
        }
        ProductVO productVO = new ProductVO();
        productVO.setSku(productEntity.getSku());
        productVO.setProductName(productEntity.getProductName());
        productVO.setCategoryId(productEntity.getCategoryId());
        productVO.setWeight(productEntity.getWeight());
        productVO.setPicturePath(productEntity.getPicturePath());
        productVO.setExtType(productEntity.getExtType());
        productVO.setSubType(productEntity.getSubType());
        productVO.setMallSkuName(productEntity.getMallSkuName());
        productVO.setMallSkuPic(productEntity.getMallSkuPic());
        productVO.setKeyValueList(toProductsPropertyValueVOList(productEntity.getKeyValueList()));
        productVO.setInventoryDetailVOS(toInventoryDetailVOList(productEntity.getInventoryList()));
        productVO.setPdNo(productEntity.getPdNo());
        productVO.setOrigin(productEntity.getOrigin() == null ? null : productEntity.getOrigin());
        productVO.setPdId(productEntity.getPdId());
        productVO.setBrandName(productEntity.getBrandName());
        productVO.setPdName(productEntity.getProductName());
        productVO.setCreateType(productEntity.getCreateType());
// Not mapped FROM fields:
// skuName
// skuPic
// pdId
        return productVO;
    }

    private static List<InventoryDetailVO> toInventoryDetailVOList(List<InventoryEntity> inventoryList) {
        if (CollectionUtils.isEmpty(inventoryList)) {
            return Collections.emptyList();
        }
        List<InventoryDetailVO> inventoryDetailVOS = new ArrayList<>(inventoryList.size());
        for (InventoryEntity inventoryEntity : inventoryList) {
            InventoryDetailVO inventoryDetailVO = new InventoryDetailVO();
            inventoryDetailVO.setOutdated(inventoryEntity.getOutdated());
            inventoryDetailVO.setSku(inventoryEntity.getSku());
            inventoryDetailVO.setPic(inventoryEntity.getSkuPic());
            inventoryDetailVO.setExtType(inventoryEntity.getExtType());
            inventoryDetailVO.setPdId(inventoryEntity.getPdId());
            inventoryDetailVO.setSubType(inventoryEntity.getSubType());
            inventoryDetailVO.setUnit(inventoryEntity.getUnit());
            try {
                inventoryDetailVO.setWeight(inventoryEntity.getWeight().substring(inventoryEntity.getWeight().indexOf("_") + 1));
            } catch (Exception e) {
                inventoryDetailVO.setWeight(inventoryEntity.getWeight());
            }
            inventoryDetailVOS.add(inventoryDetailVO);
        }
        return inventoryDetailVOS;
    }

    public static ProductVO productsEntityToProductVO(ProductsEntity productsEntity) {
        ProductVO productVO = new ProductVO();
        productVO.setProductName(productsEntity.getPdName());
        productVO.setPdName(productsEntity.getPdName());
        productVO.setCategoryId(productsEntity.getCategoryId() == null ? null : productsEntity.getCategoryId().longValue());
        productVO.setPicturePath(productsEntity.getPicturePath());
        productVO.setPdNo(productsEntity.getPdNo());
        productVO.setPdId(productsEntity.getPdId());
        productVO.setBrandId(productsEntity.getBrandId());
        productVO.setOrigin(productsEntity.getOrigin() == null ? null : productsEntity.getOrigin().toString());
        productVO.setAuditStatus(productsEntity.getAuditStatus());
        productVO.setOutdated(productsEntity.getOutdated());
        productVO.setPddetail(productsEntity.getPddetail());
        productVO.setDetailPicture(productsEntity.getDetailPicture());

        productVO.setViewCount(productsEntity.getViewCount());
        productVO.setPriority(productsEntity.getPriority());
        productVO.setAfterSaleTime(productsEntity.getAfterSaleTime());
        productVO.setAfterSaleType(productsEntity.getAfterSaleType());
        productVO.setAfterSaleUnit(productsEntity.getAfterSaleUnit());
        productVO.setCreateTime(productsEntity.getCreateTime());
        productVO.setExpireTime(productsEntity.getExpireTime());
        productVO.setStorageLocation(productsEntity.getStorageLocation());
        productVO.setStorageMethod(productsEntity.getStorageMethod());
        productVO.setSlogan(productsEntity.getSlogan());

        productVO.setOtherSlogan(productsEntity.getOtherSlogan());
        productVO.setRefundType(productsEntity.getRefundType());
        productVO.setQualityTime(productsEntity.getQualityTime());
        productVO.setQualityTimeUnit(productsEntity.getQualityTimeUnit());
        productVO.setWarnTime(productsEntity.getWarnTime());
        productVO.setAddTime(productsEntity.getAddTime());
        productVO.setCreateTime(productsEntity.getCreateTime());
        productVO.setUpdateTime(productsEntity.getUpdateTime());
        productVO.setCreator(productsEntity.getCreator());
        productVO.setRealName(productsEntity.getRealName());
        productVO.setCreateRemark(productsEntity.getCreateRemark());
        productVO.setAuditTime(productsEntity.getAuditTime());

        productVO.setProductIntroduction(productsEntity.getProductIntroduction());
        productVO.setAuditor(productsEntity.getAuditor());
        productVO.setQualityTimeType(productsEntity.getQualityTimeType());

        return productVO;
    }
}
