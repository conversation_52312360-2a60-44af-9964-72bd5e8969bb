package net.summerfarm.manage.application.inbound.controller.area.vo;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运营大区VO，带有子area的list
 */
@Data
@Accessors(chain = true)
public class LargeAreaWithSubAreaVO implements Serializable {

    /**
     * 大区名字
     */
    @ApiModelProperty(value = "运营大区名字")
    private String largeAreaName;


    /**
     * 大区AreaNO
     */
    @ApiModelProperty(value = "运营大区编号largeAreaNo")
    private Integer largeAreaNo;

    /**
     * 大区管理员ID（admin.admin_id）
     */
    @ApiModelProperty(value = "大区管理员ID（admin.admin_id）")
    private Integer manageAdminId;

    /**
     * 状态字段，0：未开启，1：正常；
     */
    @ApiModelProperty(value = "状态字段，0：未开启，1：正常；")
    private Integer status;

    /**
     * 所包含的下级area
     */
    @ApiModelProperty(value = "所包含的下级area（运营服务区）")
    private List<AreaVO> areaList;
}
