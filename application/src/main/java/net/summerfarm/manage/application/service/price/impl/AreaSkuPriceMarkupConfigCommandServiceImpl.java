package net.summerfarm.manage.application.service.price.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.inbound.controller.price.input.command.AreaSkuPriceMarkupConfigBatchUpdateInput;
import net.summerfarm.manage.application.inbound.mq.msgbody.AreaSkuPriceDTO;
import net.summerfarm.manage.application.inbound.provider.areasku.converter.AreaSkuConverter;
import net.summerfarm.manage.application.service.price.AreaSkuPriceMarkupConfigCommandService;
import net.summerfarm.manage.application.service.price.AreaSkuPriceMarkupConfigQueryService;
import net.summerfarm.manage.application.service.product.mall.AreaSkuCommandService;
import net.summerfarm.manage.application.util.UserInfoHolder;
import net.summerfarm.manage.common.constants.AppConsts;
import net.summerfarm.manage.domain.price.param.query.AreaSkuPriceMarkupConfigQueryParam;
import net.summerfarm.manage.domain.price.repository.AreaSkuPriceMarkupConfigCommandRepository;
import net.summerfarm.manage.domain.price.repository.AreaSkuPriceMarkupConfigQueryRepository;
import net.summerfarm.manage.domain.price.service.AreaSkuPriceMarkupConfigCommandDomainService;
import net.summerfarm.manage.domain.price.entity.AreaSkuPriceMarkupConfigEntity;
import net.summerfarm.manage.domain.price.param.command.AreaSkuPriceMarkupConfigCommandParam;
import net.summerfarm.manage.application.inbound.controller.price.input.command.AreaSkuPriceMarkupConfigCommandInput;
import net.summerfarm.manage.application.inbound.controller.price.assembler.AreaSkuPriceMarkupConfigAssembler;
import net.summerfarm.manage.domain.product.entity.AreaSkuEntity;
import net.summerfarm.manage.domain.product.param.command.AreaSkuPriceCommandParam;
import net.summerfarm.manage.domain.product.repository.AreaSkuCommandRepository;
import net.summerfarm.manage.domain.product.repository.AreaSkuQueryRepository;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-03-26 13:59:07
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class AreaSkuPriceMarkupConfigCommandServiceImpl implements AreaSkuPriceMarkupConfigCommandService {

    @Autowired
    private AreaSkuPriceMarkupConfigCommandDomainService areaSkuPriceMarkupConfigCommandDomainService;
    @Autowired
    private AreaSkuPriceMarkupConfigCommandRepository areaSkuPriceMarkupConfigCommandRepository;
    @Autowired
    private AreaSkuQueryRepository areaSkuQueryRepository;
    @Autowired
    private AreaSkuCommandService areaSkuCommandService;
    @Autowired
    private AreaSkuPriceMarkupConfigQueryRepository areaSkuPriceMarkupConfigQueryRepository;
    @Resource
    private AreaSkuCommandRepository areaSkuCommandRepository;


    @Override
    public AreaSkuPriceMarkupConfigEntity insert(AreaSkuPriceMarkupConfigCommandInput input) {
        // 处理配置
        AreaSkuPriceMarkupConfigCommandParam param = AreaSkuPriceMarkupConfigAssembler.INSTANCE.buildCreateParam(input);
        param.setCreator(UserInfoHolder.getAdminName());
        AreaSkuPriceMarkupConfigEntity entity = areaSkuPriceMarkupConfigCommandDomainService.insert(param);

        // 处理价格
        AreaSkuPriceCommandParam areaSkuPriceCommandParam = this.buildUpdatePriceEntity(entity.getSku(), entity.getAreaNo(), param.getMarkupValue(), BigDecimal.ZERO);
        areaSkuCommandRepository.updateAreaSkuPrice(Collections.singletonList(areaSkuPriceCommandParam));
        return entity;
    }


    @Override
    public int update(AreaSkuPriceMarkupConfigCommandInput input) {
        Long id = input.getId();
        AreaSkuPriceMarkupConfigEntity markupConfigEntity = areaSkuPriceMarkupConfigQueryRepository.selectById(id);
        if(markupConfigEntity == null) {
            log.info("加价配置不存在!");
            throw new BizException("更新失败,加价配置不存在!");
        }

        AreaSkuPriceMarkupConfigCommandParam param = AreaSkuPriceMarkupConfigAssembler.INSTANCE.buildUpdateParam(input);
        param.setUpdater(UserInfoHolder.getAdminName());
        int updateNum = areaSkuPriceMarkupConfigCommandDomainService.update(param);

        // 处理价格
        AreaSkuPriceCommandParam skuPriceCommandParam = this.buildUpdatePriceEntity(markupConfigEntity.getSku(), markupConfigEntity.getAreaNo(), param.getMarkupValue(), markupConfigEntity.getMarkupValue());
        areaSkuCommandRepository.updateAreaSkuPrice(Collections.singletonList(skuPriceCommandParam));
        return updateNum;
    }

    @Override
    public int batchUpdatePrice(AreaSkuPriceMarkupConfigBatchUpdateInput input) {
        List<Long> inputIds = input.getIds();
        AreaSkuPriceMarkupConfigQueryParam param = new AreaSkuPriceMarkupConfigQueryParam();
        param.setIds(inputIds);
        List<AreaSkuPriceMarkupConfigEntity> configEntities = areaSkuPriceMarkupConfigQueryRepository.selectByCondition(param);
        if(CollUtil.isEmpty(configEntities)) {
            log.info("加价配置不存在!configEntities:{}", JSON.toJSONString(configEntities));
            throw new BizException("更新失败,加价配置不存在!");
        }
        List<Long> updateIds = configEntities.stream().map(AreaSkuPriceMarkupConfigEntity::getId).collect(Collectors.toList());
        int updated = areaSkuPriceMarkupConfigCommandRepository.updateMarkupValueByIds(input.getMarkupValue(), updateIds);

        // 批量处理价格
        List<AreaSkuPriceCommandParam> skuPriceCommandParam = new ArrayList<>();
        configEntities.forEach(entity -> {
            skuPriceCommandParam.add(this.buildUpdatePriceEntity(entity.getSku(), entity.getAreaNo(),input.getMarkupValue(), entity.getMarkupValue()));
        });
        if(CollUtil.isNotEmpty(skuPriceCommandParam)) {
            areaSkuCommandRepository.updateAreaSkuPrice(skuPriceCommandParam);
        }
        return updated;
    }

    @Override
    public int batchInsert(List<AreaSkuPriceMarkupConfigCommandInput> inputList) {
        List<AreaSkuPriceMarkupConfigCommandParam> paramList = AreaSkuPriceMarkupConfigAssembler.INSTANCE.buildCreateParamList(inputList);
        return areaSkuPriceMarkupConfigCommandRepository.batchInsert(paramList);
    }


    /**
     * 根据加价配置构建待更新的价格数据
     * @param sku
     * @param areaNo
     * @param newMarkupValue
     * @param oldMarkupValue
     * @return
     */
    public AreaSkuPriceCommandParam buildUpdatePriceEntity(String sku, Integer areaNo, BigDecimal newMarkupValue , BigDecimal oldMarkupValue) {
        List<AreaSkuEntity> areaSkuEntities = areaSkuQueryRepository.queryListSkuPrice(Collections.singletonList(sku), Collections.singletonList(areaNo), null);
        if (CollectionUtil.isEmpty(areaSkuEntities)) {
            log.warn("分销商调价失败,area_sku信息不存在!,sku={},areaNo={}", sku, areaNo);
            throw new BizException("更新失败");
        }
        AreaSkuEntity areaSkuEntity = areaSkuEntities.get(0);
        BigDecimal price = areaSkuEntity.getPrice();
        BigDecimal newPrice = price.subtract(oldMarkupValue).add(newMarkupValue);

        AreaSkuPriceCommandParam param = new AreaSkuPriceCommandParam();
        param.setSku(sku);
        param.setAreaNo(areaNo);
        param.setPrice(newPrice);
        log.info("待更新的商品价格。dto：{}", JSON.toJSONString(param));
        return param;
    }


    @Override
    public int delete(Long id) {
        return areaSkuPriceMarkupConfigCommandDomainService.delete(id);
    }
}