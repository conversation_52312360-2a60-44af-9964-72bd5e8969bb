package net.summerfarm.manage.application.service.product.mall;

import net.summerfarm.manage.common.dto.SkuCopyDTO;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;

import java.util.List;
import java.util.Map;

public interface SkuCopyService {
    SkuCopyDTO queryTemplateSkuAndSave(Long pdId, List<InventoryEntity> templateSkuList, Integer areaNo, String storeName);

    List<InventoryEntity> queryTemplateSku(Long pdId, String weightLike);
}
