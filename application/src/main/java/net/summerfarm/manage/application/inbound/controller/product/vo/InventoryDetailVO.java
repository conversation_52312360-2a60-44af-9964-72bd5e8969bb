package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class InventoryDetailVO implements Serializable {

    private Long pdId;

    private String sku;

    /**
     * 规格
     */
    private String weight;

    /**
     * 性质
     * 类型 0 自营 1 代仓
     */
    private Integer type;
    /**
     * 商品二级性质，1 自营-代销不入仓、2 自营-代销入仓、3 自营-经销、4 代仓-代仓
     */
    private Integer subType;

    /**
     * sku性质：0、常规 1、活动 2、临保
     */
    private Integer extType;

    /**
     * 包装
     */
    private String unit;


    /**
     * sku性质
     */
    private Integer outdated;

    /**
     * sku图片
     */
    private String pic;

    /**
     * 区域sku
     */
    private List<AreaSkuVO> areaSkuVOS;

    /**
     * 销售属性
     */
    private List<ProductsPropertyValueVO> saleValueList;

    /**
     * 绑定SKU
     */
    private String bindSku;

    /**
     * 上新审核状态：0、待审核 1、审核通过 2、审核失败
     */
    private Integer auditStatus;

    /**
     * 上新状态：0、待审核 1、上新完成 2、上新失败
     */
    private Integer createStatus;

    /**
     * id
     */
    private Long invId;


    /**
     * 属性ID
     */
    private Integer aitId;

    /**
     * 销售模式
     */
    private Integer salesMode;

    /**
     * 产地
     */
    private String origin;

    /**
     * 包数
     */
    private String pack;

    /**
     * 租户ID：1-鲜沐
     */
    private Long tenantId;

    /**
     * 上新类型：0、平台 1、大客户 2、帆台代仓
     */
    private Integer createType;

    /**
     * 规格备注
     */
    private String weightNotes;

    /**
     * 是否为国产，0：不是，1是
     */
    private Integer isDomestic;

    /**
     * 体积
     */
    private String volume;

    /**
     * 重量
     */
    private BigDecimal weightNum;

    /**
     * 有效期
     */
    private String expiryDate;

    /**
     * 是否展示
     */
    private Boolean show;

    /**
     * 生熟度
     */
    private String maturity;

    /**
     * 生产日期
     */
    private Date productionDate;

    /**
     * 贮存区域
     */
    private String storageMethod;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 促销价
     */
    private BigDecimal promotionPrice;

    /**
     * 商品介绍
     */
    private String introduction;

    /**
     * 售后最大数量
     */
    private Integer afterSaleQuantity;

    /**
     * 最小起售量
     */
    private Integer baseSaleQuantity;

    /**
     * 售卖规格
     */
    private Integer baseSaleUnit;

    /**
     * 所属/大客户ID
     */
    private Integer adminId;

    /**
     * 是否放入样品池
     */
    private Integer samplePool;

    /**
     * sku头图
     */
    private String skuPic;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 供应商是否可见：0不可见，1可见
     */
    private Integer supplierVisible;


    /**
     * 上新审核时间
     */
    private LocalDateTime auditTime;


    /**
     * 创建人adminId
     */
    private Integer creator;

    /**
     * 上新备注
     */
    private String createRemark;

    /**
     * 任务类型：0、SPU 1、SKU
     */
    private Integer taskType;

    /**
     * 工商名称
     */
    private String realName;

    /**
     * 审核人adminId
     */
    private Integer auditor;

    /**
     * 0、不展示平均价 1、展示平均价
     */
    private Integer averagePriceFlag;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * spu名称
     */
    private String pdName;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 大客户名称备注
     */
    private String nameRemakes;

    /**
     * sku标签--老逻辑
     */
    private List<ProductLabelValueVO> productLabelValueVos;

    /**
     * sku标签--商品中心获取
     */
    private String itemLabel;

    /**
     * 买手ID
     */
    private Long buyerId;

    /**
     * 买手名称
     */
    private String buyerName;

    /**
     * 净重
     */
    private BigDecimal netWeightNum;

    /**
     * 净重单位
     */
    private String netWeightUnit;

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 售后规则详情
     */
    private String afterSaleRuleDetail;

    /**
     * 供应商报价类型：0-默认类型，1-按斤报价，2-按件报价
     */
    private Integer quoteType;

    /**
     * 自动补差售后量阈值
     */
    private Integer minAutoAfterSaleThreshold;
}
