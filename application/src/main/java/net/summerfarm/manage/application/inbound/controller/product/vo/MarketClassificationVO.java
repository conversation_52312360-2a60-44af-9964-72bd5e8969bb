package net.summerfarm.manage.application.inbound.controller.product.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class MarketClassificationVO implements Serializable {
    private Long marketId;
    /**
     * tenant_id
     */
    private Long tenantId;

    /**
     * 一级分类Id
     */
    private Long firstClassificationId;
    /**
     * 一级分类名称
     */
    private String firstClassificationName;

    /**
     * 二级分类Id
     */
    private Long secondClassificationId;
    /**
     * 二级分类
     */
    private String secondClassificationName;
    /**
     * 分类全名
     */
    private String classificationFullName;
}