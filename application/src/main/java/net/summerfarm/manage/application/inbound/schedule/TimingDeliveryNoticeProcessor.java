package net.summerfarm.manage.application.inbound.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.application.service.delivery.DeliveryCommandService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @ClassName TimingDeliveryNoticeProcessor
 * @Description 省心送配送计划通知
 * <AUTHOR>
 * @Date 14:05 2024/2/23
 * @Version 1.0
 **/
@Component
@Slf4j
public class TimingDeliveryNoticeProcessor extends XianMuJavaProcessorV2 {

    @Resource
    private DeliveryCommandService deliveryCommandService;

    private static final String ORDER_NO_KEY = "orderNo";

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("省心送配送计划通知 start :{}", LocalDateTime.now());
        String orderNo = null;
        if (StringUtils.isNotBlank(context.getInstanceParameters())) {
            JSONObject jsonObject = JSON.parseObject(context.getInstanceParameters());
            if (jsonObject.containsKey(ORDER_NO_KEY)) {
                orderNo = jsonObject.getString(ORDER_NO_KEY);
            }
        }
        deliveryCommandService.timingDeliveryNotice(orderNo);
        log.info("省心送配送计划通知 end :{}", LocalDateTime.now());
        return new ProcessResult(true);
    }
}
