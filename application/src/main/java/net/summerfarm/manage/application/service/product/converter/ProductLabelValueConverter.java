package net.summerfarm.manage.application.service.product.converter;

import net.summerfarm.manage.application.inbound.controller.product.input.ProductLabelValueInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.ProductLabelValueVO;
import net.summerfarm.manage.domain.product.entity.ProductLabelValueEntity;
import net.summerfarm.manage.domain.product.param.command.ProductLabelValueCommandParam;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName ProductLabelValueConverter
 * @Description
 * <AUTHOR>
 * @Date 19:05 2024/5/7
 * @Version 1.0
 **/
public class ProductLabelValueConverter {
    public static ProductLabelValueCommandParam inputToCommonParam(ProductLabelValueInput productLabelValueVo) {
        ProductLabelValueCommandParam param = new ProductLabelValueCommandParam();
        param.setId(productLabelValueVo.getId());
        param.setLabelValue(productLabelValueVo.getLabelValue());
        param.setSku(productLabelValueVo.getSku());
        param.setLabelId(productLabelValueVo.getLabelId() == null ? null : productLabelValueVo.getLabelId().toString());
        return param;
    }

    public static List<ProductLabelValueVO> entityToVO(List<ProductLabelValueEntity> productLabelValueEntities) {
        if (CollectionUtils.isEmpty(productLabelValueEntities)) {
            return Collections.emptyList();
        }

        List<ProductLabelValueVO> productLabelValueVOS = new ArrayList<>();
        for (ProductLabelValueEntity productLabelValueEntity : productLabelValueEntities) {
            ProductLabelValueVO productLabelValueVO = new ProductLabelValueVO();
            productLabelValueVO.setId(productLabelValueEntity.getId());
            productLabelValueVO.setLabelValue(productLabelValueEntity.getLabelValue());
            productLabelValueVO.setSku(productLabelValueEntity.getSku());
            productLabelValueVO.setLabelId(productLabelValueEntity.getLabelId());
            productLabelValueVO.setLabelField(productLabelValueEntity.getLabelField());
            productLabelValueVO.setLabelName(productLabelValueEntity.getLabelName());
            productLabelValueVOS.add(productLabelValueVO);
        }
        return productLabelValueVOS;
    }
}
