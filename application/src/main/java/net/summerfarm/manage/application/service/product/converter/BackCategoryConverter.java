package net.summerfarm.manage.application.service.product.converter;

import net.summerfarm.manage.application.inbound.controller.product.vo.BackCategoryVO;
import net.summerfarm.manage.domain.product.entity.CategoryEntity;

import java.util.Objects;

/**
 * @Description
 * @Date 2025/4/27 15:45
 * @<AUTHOR>
 */
public class BackCategoryConverter {

    public static BackCategoryVO convert(CategoryEntity categoryEntity) {
        if (Objects.isNull(categoryEntity)) {
            return null;
        }
        return BackCategoryVO.builder()
                .id(categoryEntity.getId())
                .category(categoryEntity.getCategory())
                .parentId(categoryEntity.getParentId())
                .type(categoryEntity.getType())
                .build();
    }

}
