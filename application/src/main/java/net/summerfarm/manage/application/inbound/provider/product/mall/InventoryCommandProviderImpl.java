package net.summerfarm.manage.application.inbound.provider.product.mall;

import net.summerfarm.client.provider.product.mall.InventoryCommandProvider;
import net.summerfarm.client.provider.product.mall.req.PopGoodsEditReq;
import net.summerfarm.manage.application.service.product.command.PopSkuUpdateCommand;
import net.summerfarm.manage.application.service.product.converter.InventoryConverter;
import net.summerfarm.manage.application.service.product.mall.InventoryCommandService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Description
 * @Date 2024/7/30 11:37
 * @<AUTHOR>
 */
@DubboService
public class InventoryCommandProviderImpl implements InventoryCommandProvider {

    @Resource
    private InventoryCommandService inventoryCommandService;

    @Override
    public DubboResponse<Boolean> editPopGoods(@Valid PopGoodsEditReq editReq) {
        PopSkuUpdateCommand updateCommand = InventoryConverter.convert(editReq);
        inventoryCommandService.updatePopSkuInfo(updateCommand);
        return DubboResponse.getOK(Boolean.TRUE);
    }
}
