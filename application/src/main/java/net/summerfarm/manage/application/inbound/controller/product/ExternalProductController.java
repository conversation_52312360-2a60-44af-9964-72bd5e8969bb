package net.summerfarm.manage.application.inbound.controller.product;

import com.aliyun.odps.utils.StringUtils;
import com.github.pagehelper.PageInfo;
import net.summerfarm.manage.application.inbound.controller.product.vo.BackCategoryVO;
import net.summerfarm.manage.application.inbound.controller.product.vo.ExternalPopProductMappingDetailVO;
import net.summerfarm.manage.common.converter.PageInfoConverter;
import net.summerfarm.manage.common.enums.products.ExternalProductMappingEnum;
import net.summerfarm.manage.domain.product.entity.CategoryEntity;
import net.summerfarm.util.ExceptionUtil;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;
import net.summerfarm.manage.application.service.product.mall.ExternalProductMappingCommandService;
import net.summerfarm.manage.application.service.product.mall.ExternalProductMappingQueryService;
import net.summerfarm.manage.application.inbound.controller.product.input.ExternalProductMappingQueryInput;
import net.summerfarm.manage.domain.product.entity.ExternalProductMappingEntity;
import net.summerfarm.manage.application.inbound.controller.product.assembler.ExternalProductMappingAssembler;
import net.summerfarm.manage.application.inbound.controller.product.input.ExternalProductMappingCommandInput;
import net.summerfarm.manage.application.inbound.controller.product.vo.ExternalPopProductMappingVO;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title 外部平台商品映射表
 * @Description 外部平台商品映射表功能模块
 * <AUTHOR>
 * @date 2024-11-15 14:13:27
 * @version 1.0
 */
@RestController
@RequestMapping(value="/externalProduct")
public class ExternalProductController {

	@Resource
	private ExternalProductMappingCommandService externalProductMappingCommandService;
	@Resource
	private ExternalProductMappingQueryService externalProductMappingQueryService;


	/**
	 * 待绑定商品列表
	 *
	 * <AUTHOR>
	 * @date 2024/11/15 15:18
	 */
	@PostMapping(value="/unmappedProduct/query/page")
	public CommonResult<PageInfo<ExternalPopProductMappingVO>> getPageUnmapped(@RequestBody ExternalProductMappingQueryInput input){
		ExceptionUtil.checkAndThrow(Objects.nonNull(input.getPageIndex())
				&& Objects.nonNull(input.getPageSize()), "分页参数不能为空");
		PageInfo<ExternalProductMappingEntity> page = externalProductMappingQueryService.getPageUnmapped(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, ExternalProductMappingAssembler::convert));
	}

	/**
	 * 查询待绑定商品类目列表
	 *
	 * <AUTHOR>
	 * @date 2024/11/15 15:24
	 */
	@PostMapping(value="/unmappedCategory/query/list")
	public CommonResult<List<BackCategoryVO>> getListUnmappedCategory(@RequestBody ExternalProductMappingQueryInput input){
		List<CategoryEntity> categoryEntityList = externalProductMappingQueryService.getListUnmappedCategory(input);
		List<BackCategoryVO> backCategoryVOList = categoryEntityList.stream().map(ExternalProductMappingAssembler::convert).collect(Collectors.toList());
		return CommonResult.ok(backCategoryVOList);
	}

	/**
	 * 已绑定商品列表
	 *
	 * <AUTHOR>
	 * @date 2024/11/15 15:09
	 */
	@PostMapping(value="/mappedProduct/query/page")
	public CommonResult<PageInfo<ExternalPopProductMappingVO>> getPageMapped(@RequestBody ExternalProductMappingQueryInput input){
		ExceptionUtil.checkAndThrow(Objects.nonNull(input.getPageIndex())
				&& Objects.nonNull(input.getPageSize()), "分页参数不能为空");
		ExceptionUtil.checkAndThrow(Objects.nonNull(input.getBuyerId()), "买手信息不能为空");
		PageInfo<ExternalProductMappingEntity> page = externalProductMappingQueryService.getPageMapped(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, ExternalProductMappingAssembler::convert));
	}


	/**
	 * 查询已绑定商品类目列表
	 * 
	 * <AUTHOR>
	 * @date 2024/11/18 16:38
	 */
	@PostMapping(value="/mappedCategory/query/list")
	public CommonResult<List<BackCategoryVO>> getListMappedCategory(@RequestBody ExternalProductMappingQueryInput input){
		ExceptionUtil.checkAndThrow(Objects.nonNull(input.getBuyerId()), "买手信息不能为空");
		List<CategoryEntity> categoryEntityList = externalProductMappingQueryService.getListMappedCategory(input);
		List<BackCategoryVO> backCategoryVOList = categoryEntityList.stream().map(ExternalProductMappingAssembler::convert).collect(Collectors.toList());
		return CommonResult.ok(backCategoryVOList);
	}

	/**
	 * 提交外部商品绑定关系
	 * @return ExternalProductMappingVO
	 */
	@PostMapping(value = "/upsert/insert")
	public CommonResult<Boolean> insert(@RequestBody ExternalProductMappingCommandInput input) {
		ExceptionUtil.checkAndThrow(StringUtils.isNotBlank(input.getXmSkuCode())
				&& StringUtils.isNotBlank(input.getExternalSkuCode()), "商品编码不能为空");
		input.setType(ExternalProductMappingEnum.SKU.getValue());
		externalProductMappingCommandService.insert(input);
		return CommonResult.ok(Boolean.TRUE);
	}

	/**
	 * 已绑定商品列表（工具页）
	 *
	 * <AUTHOR>
	 * @date 2024/11/15 15:09
	 */
	@PostMapping(value="/externalProductMapping/query/page")
	public CommonResult<PageInfo<ExternalPopProductMappingDetailVO>> pageQueryExternalProductMapping(@RequestBody ExternalProductMappingQueryInput input){
		ExceptionUtil.checkAndThrow(Objects.nonNull(input.getPageIndex())
				&& Objects.nonNull(input.getPageSize()), "分页参数不能为空");
		PageInfo<ExternalProductMappingEntity> page = externalProductMappingQueryService.pageQueryExternalProductMapping(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, ExternalProductMappingAssembler::convertDetail));
	}

	/**
	 * 解除外部商品绑定关系（工具页）
	 *
	 * <AUTHOR>
	 * @date 2024/11/18 17:20
	 */
	@PostMapping(value = "/upsert/delete")
	public CommonResult<Boolean> delete(@RequestBody ExternalProductMappingCommandInput input) {
		ExceptionUtil.checkAndThrow(Objects.nonNull(input.getId()), "id不能为空");
		externalProductMappingCommandService.delete(input.getId());
		return CommonResult.ok(Boolean.TRUE);
	}

	/**
	 * 编辑外部商品绑定关系
	 * @return
	 */
	@PostMapping(value = "/upsert/update")
	public CommonResult<Boolean> update(@RequestBody ExternalProductMappingCommandInput input) {
		ExceptionUtil.checkAndThrow(StringUtils.isNotBlank(input.getXmSkuCode())
				&& StringUtils.isNotBlank(input.getExternalSkuCode()), "商品编码不能为空");
		externalProductMappingCommandService.update(input);
		return CommonResult.ok(Boolean.TRUE);
	}

}

