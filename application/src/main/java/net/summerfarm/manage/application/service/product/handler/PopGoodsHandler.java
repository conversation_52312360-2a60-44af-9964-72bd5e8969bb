package net.summerfarm.manage.application.service.product.handler;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.manage.application.service.product.command.PopSkuUpdateCommand;
import net.summerfarm.manage.common.enums.SubTypeEnum;
import net.summerfarm.manage.domain.product.entity.InventoryEntity;
import net.summerfarm.manage.domain.product.entity.ProductsEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryCommandParam;
import net.summerfarm.manage.domain.product.param.command.ProductsCommandParam;
import net.summerfarm.manage.domain.product.repository.InventoryQueryRepository;
import net.summerfarm.manage.domain.product.service.InventoryCommandDomainService;
import net.summerfarm.manage.domain.product.service.ProductsCommandDomainService;
import net.summerfarm.util.ExceptionUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @Description
 * @Date 2024/7/30 14:00
 * @<AUTHOR>
 */
@Component
@Slf4j
public class PopGoodsHandler {

    @Resource
    private InventoryCommandDomainService inventoryCommandDomainService;
    @Resource
    private ProductsCommandDomainService productsCommandDomainService;

    public void handlePopVideo(PopSkuUpdateCommand updateCommand, InventoryEntity originalInventory) {
        String videoUrl = updateCommand.getVideoUrl();
        // 更新视频信息，支持更新为空
        if(!Objects.equals(videoUrl, originalInventory.getVideoUrl())) {
            InventoryCommandParam commandParam = new InventoryCommandParam();
            commandParam.setSku(updateCommand.getSku());
            commandParam.setVideoUrl(updateCommand.getVideoUrl());
            commandParam.setVideoUploadUser(updateCommand.getVideoUploadUser());
            commandParam.setVideoUploadTime(LocalDateTime.now());
            inventoryCommandDomainService.update(commandParam);
        }

    }

    public void handlePopPicture(PopSkuUpdateCommand updateCommand, ProductsEntity originalProducts) {
        // 支持更新为空
        ProductsCommandParam productsCommandParam = new ProductsCommandParam();
        productsCommandParam.setPdId(originalProducts.getPdId());
        productsCommandParam.setPicturePath(updateCommand.getPicturePath());
        productsCommandParam.setDetailPicture(this.buildDetailPicture(updateCommand.getDetailPictureList()));
        productsCommandDomainService.update(productsCommandParam);
        // 同步更新sku头图
        InventoryCommandParam commandParam = new InventoryCommandParam();
        commandParam.setSku(updateCommand.getSku());
        commandParam.setSkuPic(updateCommand.getPicturePath());
        inventoryCommandDomainService.update(commandParam);
    }

    private String buildDetailPicture(List<String> detailPictureList) {
        if (CollectionUtils.isEmpty(detailPictureList)) {
            return StringUtil.EMPTY_STRING;
        }
        return Joiner.on(",").join(detailPictureList);
    }


}
