package net.summerfarm.manage.facade.wms;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.manage.facade.wms.dto.WarehouseStorageDTO;
import net.summerfarm.wnc.client.provider.warehouse.WarehouseStorageQueryProvider;
import net.summerfarm.wnc.client.req.WarehouseListQuery;
import net.summerfarm.wnc.client.resp.WarehouseStorageDetailResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
public class WarehouseStorageFacade {

    @DubboReference
    private WarehouseStorageQueryProvider warehouseStorageQueryProvider;

    /**
     * 查询所有库存仓
     * @return
     */
    public List<WarehouseStorageDTO> queryAllWarehouses() {
        WarehouseListQuery query = new WarehouseListQuery();
        query.setTenantId(1L);
        DubboResponse<List<WarehouseStorageDetailResp>> dubboResponse = warehouseStorageQueryProvider.queryWarehouseList(query);
        if (null == dubboResponse || !dubboResponse.isSuccess() || CollectionUtils.isEmpty(dubboResponse.getData())) {
            log.error("Warehouse查询失败");
            return Collections.emptyList();
        } else {
            return dubboResponse.getData().stream().map(warehouseStorageDetailResp -> {
                WarehouseStorageDTO warehouseStorageDTO = new WarehouseStorageDTO();
                warehouseStorageDTO.setWarehouseNo(warehouseStorageDetailResp.getWarehouseNo());
                warehouseStorageDTO.setWarehouseName(warehouseStorageDetailResp.getWarehouseName());
                warehouseStorageDTO.setTenantId(warehouseStorageDetailResp.getTenantId());
                return warehouseStorageDTO;
            }).collect(Collectors.toList());
        }
    }
}
