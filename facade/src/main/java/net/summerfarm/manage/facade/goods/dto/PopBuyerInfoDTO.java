package net.summerfarm.manage.facade.goods.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description
 * @Date 2024/7/3 18:05
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PopBuyerInfoDTO implements Serializable {

    private static final long serialVersionUID = 7205450611915961466L;

    /**
     * 买手id
     */
    private Long buyerId;

    /**
     * 买手花名
     */
    private String buyerName;

    /**
     * 员工adminId
     */
    private Integer adminId;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 操作人
     */
    private String operator;

}
