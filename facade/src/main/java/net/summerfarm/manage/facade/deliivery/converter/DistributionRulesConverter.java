package net.summerfarm.manage.facade.deliivery.converter;


import net.summerfarm.manage.facade.deliivery.dto.ConditionsDTO;
import net.summerfarm.manage.facade.deliivery.dto.DeliveryFeeRuleDTO;
import net.summerfarm.manage.facade.deliivery.dto.DistributionRulesDTO;
import net.summerfarm.manage.facade.deliivery.dto.RulesDTO;
import net.xianmu.marketing.center.client.freight.resp.*;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-manage
 * @description
 * @date 2023/10/12 16:57:13
 */
public class DistributionRulesConverter {

    public static DistributionRulesDTO toDistributionRulesDTO(DistributionRulesDetailResp rulesDetailResp) {
        if (Objects.isNull(rulesDetailResp)) {
            return null;
        }
        DistributionRulesDTO distributionRulesDTO = new DistributionRulesDTO();
        distributionRulesDTO.setType(rulesDetailResp.getType());
        distributionRulesDTO.setTypeId(rulesDetailResp.getTypeId());
        List<RulesResp> rulesResps = rulesDetailResp.getRulesResps();
        if (CollectionUtils.isEmpty(rulesResps)) {
            return distributionRulesDTO;
        }

        //品牌管理单独处理-多个城市情况
        List<RulesDTO> rulesDTOS = new ArrayList<>();
        rulesResps.stream().forEach(rulesResp -> {
            RulesDTO rulesDTO = new RulesDTO();
            rulesDTO.setAgeing(rulesResp.getAgeing());
            rulesDTO.setAreaNo(rulesResp.getAreaNo());
            rulesDTO.setDeliveryFee(rulesResp.getDeliveryFee());
            rulesDTO.setExpressFee(rulesResp.getExpressFee());
            List<ConditionsResp> conditionsDTOS = rulesResp.getConditionsResps();
            if (!CollectionUtils.isEmpty(conditionsDTOS)) {
                List<ConditionsDTO> conditionsDTOList = new ArrayList<>(conditionsDTOS.size());
                conditionsDTOS.stream().forEach(conditionsDTO -> {
                    ConditionsDTO conditionsDTO1 = new ConditionsDTO();
                    conditionsDTO1.setAmount(conditionsDTO.getAmount());
                    conditionsDTO1.setNumber(conditionsDTO.getNumber());
                    conditionsDTO1.setProductType(conditionsDTO.getProductType());
                    conditionsDTO1.setSillType(conditionsDTO.getSillType());
                    conditionsDTOList.add(conditionsDTO1);
                });
                rulesDTO.setConditionsDTOS(conditionsDTOList);
            }
            rulesDTOS.add(rulesDTO);
        });
        distributionRulesDTO.setRulesDTOS(rulesDTOS);
        return distributionRulesDTO;
    }

    public static List<DeliveryFeeRuleDTO> toDeliveryFeeRuleDTOList(DeliveryFeeRuleInfoResp resp) {
        if (resp == null || CollectionUtils.isEmpty(resp.getDeliveryFeeRuleResps())) {
            return new ArrayList<>();
        }
        List<DeliveryFeeRuleDTO> deliveryFeeRuleDTOList = new ArrayList<>();
        for (DeliveryFeeRuleResp deliveryFeeRuleResp : resp.getDeliveryFeeRuleResps()) {
            DeliveryFeeRuleDTO deliveryFeeRuleDTO = new DeliveryFeeRuleDTO();
            deliveryFeeRuleDTO.setAgeing(deliveryFeeRuleResp.getAgeing());
            deliveryFeeRuleDTO.setStartDeliveryAmount(deliveryFeeRuleResp.getStartDeliveryAmount());
            List<DeliveryFeeRuleDTO.DeliveryFeeRuleDetailDTO> dettailDTOList = new ArrayList<>();
            deliveryFeeRuleDTO.setDeliveryFeeRuleDetailDTOList(dettailDTOList);
            deliveryFeeRuleDTOList.add(deliveryFeeRuleDTO);
            if (CollectionUtils.isEmpty(deliveryFeeRuleResp.getDeliveryFeeRuleDetailResps())) {
                continue;
            }
            for (DeliveryFeeRuleDetailResp detailResp : deliveryFeeRuleResp.getDeliveryFeeRuleDetailResps()) {
                DeliveryFeeRuleDTO.DeliveryFeeRuleDetailDTO detailDTO = new DeliveryFeeRuleDTO.DeliveryFeeRuleDetailDTO();
                detailDTO.setCategoryType(detailResp.getCategoryType());
                detailDTO.setFeeMode(detailResp.getFeeMode());
                detailDTO.setStepValue(detailResp.getStepValue());
                detailDTO.setDeliveryFee(detailResp.getDeliveryFee());
                detailDTO.setExpressFee(detailResp.getExpressFee());
                dettailDTOList.add(detailDTO);
            }
        }
        return deliveryFeeRuleDTOList;
    }
}
