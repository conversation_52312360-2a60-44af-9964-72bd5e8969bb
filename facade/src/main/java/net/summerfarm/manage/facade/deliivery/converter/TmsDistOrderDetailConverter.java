package net.summerfarm.manage.facade.deliivery.converter;

import net.summerfarm.manage.facade.deliivery.dto.DistDeliveryBatchDTO;
import net.summerfarm.manage.facade.deliivery.dto.DistDeliverySiteDTO;
import net.summerfarm.manage.facade.deliivery.dto.DistOrderDTO;
import net.summerfarm.manage.facade.deliivery.input.DistOrderDetailInput;
import net.summerfarm.tms.client.dist.req.standard.DistOrderQueryStandardReq;
import net.summerfarm.tms.client.dist.resp.standard.DistDeliveryBatchStandardResp;
import net.summerfarm.tms.client.dist.resp.standard.DistDeliverySiteStandardResp;
import net.summerfarm.tms.client.dist.resp.standard.DistOrderStandardResp;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * @ClassName TmsDistOrderDetailConverter
 * @Description TODO
 * <AUTHOR>
 * @Date 18:34 2024/1/17
 * @Version 1.0
 **/
public class TmsDistOrderDetailConverter {

    public static DistOrderDTO convertTmsDistOrderResp2DTO(DistOrderStandardResp resp) {
        DistOrderDTO distOrderDTO = new DistOrderDTO();
        if (ObjectUtils.isEmpty(resp)){
            return distOrderDTO;
        }
        distOrderDTO.setDistId(resp.getDistId());
        distOrderDTO.setExpectBeginTime(resp.getExpectBeginTime());
        distOrderDTO.setRealArrivalTime(resp.getRealArrivalTime());
        distOrderDTO.setOutOrderId(resp.getOutOrderId());
        distOrderDTO.setStatus(resp.getStatus());
        distOrderDTO.setDeliveryBatch(convertTmsDistOrderResp2DTO(resp.getBatchMessage()));
        distOrderDTO.setDeliverySite(convertTmsDistOrderResp2DTO(resp.getDeliverySiteMessage()));
        return distOrderDTO;
    }

    public static DistDeliveryBatchDTO convertTmsDistOrderResp2DTO(DistDeliveryBatchStandardResp resp) {
        DistDeliveryBatchDTO distDeliveryBatchDTO = new DistDeliveryBatchDTO();
        if (ObjectUtils.isEmpty(resp)){
            return distDeliveryBatchDTO;
        }
        distDeliveryBatchDTO.setDeliveryBatchId(resp.getDeliveryBatchId());
        distDeliveryBatchDTO.setDeliveryTime(resp.getDeliveryTime());
        distDeliveryBatchDTO.setDriverId(resp.getDriverId());
        distDeliveryBatchDTO.setDriver(resp.getDriver());
        distDeliveryBatchDTO.setDriverPhone(resp.getDriverPhone());
        distDeliveryBatchDTO.setCarNumber(resp.getCarNumber());
        distDeliveryBatchDTO.setPathName(resp.getPathName());
        distDeliveryBatchDTO.setPathCode(resp.getPathCode());
        return distDeliveryBatchDTO;

    }

    public static DistDeliverySiteDTO convertTmsDistOrderResp2DTO(DistDeliverySiteStandardResp resp) {
        DistDeliverySiteDTO distDeliverySiteDTO = new DistDeliverySiteDTO();
        if (ObjectUtils.isEmpty(resp)){
            return distDeliverySiteDTO;
        }
        distDeliverySiteDTO.setId(resp.getId());
        distDeliverySiteDTO.setSequence(resp.getSequence());
        distDeliverySiteDTO.setStatus(resp.getStatus());
        distDeliverySiteDTO.setSignInPics(resp.getSignInPics());
        distDeliverySiteDTO.setSignInSignPic(resp.getSignInSignPic());
        distDeliverySiteDTO.setSignInProductPic(resp.getSignInProductPic());
        return distDeliverySiteDTO;
    }

    public static DistOrderQueryStandardReq convertTmsDistOrderReq(DistOrderDetailInput input) {
        DistOrderQueryStandardReq distOrderQueryStandardReq = new DistOrderQueryStandardReq();
        distOrderQueryStandardReq.setOuterOrderId(input.getOrderNo());
        distOrderQueryStandardReq.setOuterContactId(String.valueOf(input.getContactId()));
        distOrderQueryStandardReq.setExpectBeginTime(LocalDateTime.of(input.getDeliveryTime(), LocalTime.MIN));
        distOrderQueryStandardReq.setSource(input.getSource());
        return distOrderQueryStandardReq;
    }
}
