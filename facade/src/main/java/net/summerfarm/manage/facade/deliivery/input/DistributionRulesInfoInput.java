package net.summerfarm.manage.facade.deliivery.input;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @project sf-mall-manage
 * @description
 * @date 2023/11/16 17:05:45
 */
@Data
public class DistributionRulesInfoInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 类型ID 服务区域、地址、品牌主键ID
     */
    private Long typeId;

    /**
     * 规则类型 1：门店管理 2：品牌管理 3：服务区域
     */
    private Integer type;
}
