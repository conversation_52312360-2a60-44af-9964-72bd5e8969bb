package net.summerfarm.manage.facade.wiki.dto;

import lombok.Data;

import java.util.List;

/**
 * 知识库搜索结果项
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
public class SearchResultItem {
    
    /**
     * 结果ID
     */
    private String id;
    
    /**
     * 更新时间
     */
    private String updateTime;
    
    /**
     * 问题
     */
    private String q;
    
    /**
     * 答案
     */
    private String a;
    
    /**
     * 块索引
     */
    private Integer chunkIndex;
    
    /**
     * 数据集ID
     */
    private String datasetId;
    
    /**
     * 集合ID
     */
    private String collectionId;
    
    /**
     * 源ID
     */
    private String sourceId;
    
    /**
     * 源名称
     */
    private String sourceName;
    
    /**
     * 评分列表
     */
    private List<ScoreItem> score;
    
    /**
     * Token数量
     */
    private Integer tokens;
}
