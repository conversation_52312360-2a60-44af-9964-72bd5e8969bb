package net.summerfarm.manage.facade.wnc.convert;

import net.summerfarm.manage.facade.wnc.dto.AreaWarehouseNoSkuDTO;
import net.summerfarm.manage.facade.wnc.input.SkuWarehouseNoQueryAreaInput;
import net.summerfarm.wnc.client.req.fence.AreaQueryWarehouseNoSkuReq;
import net.summerfarm.wnc.client.req.fence.SkuWarehouseNoQueryAreaReq;
import net.summerfarm.wnc.client.resp.fence.AreaWarehouseNoSkuResp;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import net.summerfarm.manage.common.enums.MerchantEnums;
import net.summerfarm.wnc.client.enums.SourceEnum;

/**
 * <AUTHOR>
 * @Date 2025/7/16 14:27
 * @Version 1.0
 */
public class WncConverter {

    public static AreaQueryWarehouseNoSkuReq toAreaQueryWarehouseNoSkuReq(List<SkuWarehouseNoQueryAreaInput> inputs) {
        AreaQueryWarehouseNoSkuReq req = new AreaQueryWarehouseNoSkuReq();
        List<SkuWarehouseNoQueryAreaReq> skuWarehouseNoQueryAreaReqList = new ArrayList<>();

        inputs.forEach(input -> {
            SkuWarehouseNoQueryAreaReq req1 = new SkuWarehouseNoQueryAreaReq();
            req1.setSku(input.getSku());
            req1.setWarehouseNo(input.getWarehouseNo());
            skuWarehouseNoQueryAreaReqList.add(req1);
        });
        req.setSkuWarehouseNoQueryAreaReqList(skuWarehouseNoQueryAreaReqList);
        return req;
    }

    public static List<AreaWarehouseNoSkuDTO> toAreaWarehouseNoSkuDTOList(List<AreaWarehouseNoSkuResp> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        List<AreaWarehouseNoSkuDTO> areaWarehouseNoSkuDTOS = new ArrayList<>();
        data.forEach(item -> {
            AreaWarehouseNoSkuDTO areaWarehouseNoSkuDTO = new AreaWarehouseNoSkuDTO();
            areaWarehouseNoSkuDTO.setSku(item.getSku());
            areaWarehouseNoSkuDTO.setWarehouseNo(item.getWarehouseNo());
            areaWarehouseNoSkuDTO.setAreaNos(item.getAreaNos());
            areaWarehouseNoSkuDTOS.add(areaWarehouseNoSkuDTO);
        });
        return areaWarehouseNoSkuDTOS;
    }
    public static SourceEnum getSourceEnum(Integer businessLine) {
        if (MerchantEnums.BusinessLineEnum.POP.getCode().equals(businessLine)) {
            return SourceEnum.POP_MALL;
        } else {
            return SourceEnum.XM_MALL;
        }
    }
}
