package net.summerfarm.manage.facade.message.input;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/24  16:28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FeiShuMsgInput{

    /**
     * 接收人userId
     */
    private String userIdList;

    /**
     * text、markdown、link
     */
    private String msgType;

    /**
     * txt内容
     */
    private String content;

    /**
     * markdown 标题
     */
    private String title;

    /**
     * markdown 文本
     */
    private String text;

    /**
     * link 消息路径
     */
    private String messageUrl;

    /**
     * link 图片路径
     */
    private String picUrl;
    /**
     * 消息接收用户Id：adminId、tms 司机Id、srm 供应商Id
     */
    private List<Long> receiverIdList;


}
