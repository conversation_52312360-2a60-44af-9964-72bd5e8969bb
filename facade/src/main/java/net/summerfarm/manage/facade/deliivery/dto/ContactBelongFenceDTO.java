package net.summerfarm.manage.facade.deliivery.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @project summerfarm-mall
 * @description
 * @date 2024/1/4 13:40:20
 */
@Data
public class ContactBelongFenceDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 地址ID
     */
    private Long contactId;

    /**
     * 围栏状态 0：正常，3：暂停
     */
    private Integer status;

    /**
     * 围栏名称
     */
    private String fenceName;
}
