package net.summerfarm.manage.facade.pms.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description
 * @Date 2024/12/12 10:54
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PopSkuCostFacadeDTO {

    /**
     * sku编码
     */
    private String sku;

    /**
     * 供应商毛重成本（斤）
     */
    private BigDecimal supplierWeightPrice;

    /**
     * 供应商整件成本
     */
    private BigDecimal supplierCost;
}
