package net.summerfarm.manage.domain.product.entity;

import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-05-07 11:31:28
 * @version 1.0
 *
 */
@Data
public class ProductsEntity {
	/**
	 * 
	 */
	private Long pdId;

	/**
	 * 分类
	 */
	private Integer categoryId;

	/**
	 * 品牌
	 */
	private Integer brandId;

	/**
	 * 商品名称
	 */
	private String pdName;

	/**
	 * 商品描述
	 */
	private String pddetail;

	/**
	 * 详情图片
	 */
	private String detailPicture;

	/**
	 * 浏览次数
	 */
	private Long viewCount;

	/**
	 * 
	 */
	private Integer priority;

	/**
	 * 从截单开始的售后时间？？
	 */
	private Integer afterSaleTime;

	/**
	 * 售后类型
	 */
	private String afterSaleType;

	/**
	 * 售后单位
	 */
	private String afterSaleUnit;

	/**
	 * 上架时间
	 */
	private LocalDateTime createTime;

	/**
	 * 下架时间
	 */
	private LocalDateTime expireTime;

	/**
	 * SPU生命周期：-1、上新中 0、有效 1、已删除
	 */
	private Integer outdated;

	/**
	 * 仓储区域
	 */
	private Integer storageLocation;

	/**
	 * 
	 */
	private String pdNo;

	/**
	 * 
	 */
	private Integer origin;

	/**
	 * 
	 */
	private String storageMethod;

	/**
	 * 
	 */
	private String slogan;

	/**
	 * 
	 */
	private String otherSlogan;

	/**
	 * 
	 */
	private String picturePath;

	/**
	 * 退款原因
	 */
	private String refundType;

	/**
	 * 保质期时长
	 */
	private Integer qualityTime;

	/**
	 * 保质期时长单位
	 */
	private String qualityTimeUnit;

	/**
	 * 临保预警时长
	 */
	private Integer warnTime;

	/**
	 * 创建时间
	 */
	private LocalDateTime addTime;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建人adminId
	 */
	private Integer creator;

	/**
	 * 上新类型：0、平台 1、大客户
	 */
	private Integer createType;

	/**
	 * 商品实物名
	 */
	private String realName;

	/**
	 * 上新备注
	 */
	private String createRemark;

	/**
	 * 上新审核状态：0、待上新 1、上新成功 2、上新失败
	 */
	private Integer auditStatus;

	/**
	 * 上新审核时间
	 */
	private LocalDateTime auditTime;

	/**
	 * 商品介绍信息
	 */
	private String productIntroduction;

	/**
	 * 操作人adminId
	 */
	private Integer auditor;

	/**
	 * 保质期时长类型, 0 固定时长, 1 到期时间
	 */
	private Integer qualityTimeType;

	

	
}