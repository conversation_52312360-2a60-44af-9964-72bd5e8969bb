package net.summerfarm.manage.domain.activity.valueObject;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @descripton
 * @date 2024/5/14 14:34
 */
@Data
@Slf4j
public class ActivityLadderPriceValueObject implements Serializable {

    /**
     * 阶梯数
     */
    private Integer unit;

    /**
     * 阶梯价格
     */
    private BigDecimal price;

    /**
     * 价格调整方式：0：指定价 1：百分比 2：定额减 3:毛利百分比
     */
    private Integer adjustType;

    /**
     * 价格或百分比分子
     */
    private BigDecimal amount;

    /**
     * 小数处理逻辑：0、四舍五入保留两位小数 1、向上取整
     */
    private Integer roundingMode;



    public static BigDecimal getLadderPriceByUnit(List<ActivityLadderPriceValueObject> priceList, Integer unit, BigDecimal salePrice) {
        if(CollectionUtils.isEmpty(priceList)) {
            log.info("阶梯价列表为空！");
            return salePrice;
        }
        if(unit == null || unit <= 0) {
            log.info("计算活动阶梯价时，购买数量异常, unit:{}", unit);
            unit = 1;
        }

        ActivityLadderPriceValueObject validLadder = null;
        for (ActivityLadderPriceValueObject vo : priceList) {
            if (vo.getUnit() <= unit) {
                if (validLadder == null || validLadder.getUnit() < vo.getUnit()) {
                    validLadder = vo;
                }
            }
        }
        return validLadder == null ? salePrice : validLadder.getPrice();
    }

    /**
     * 获取活动价格 -- 根据数量（阶梯）
     * @param ladderPrice
     * @param quantity
     * @return
     */
    public static ActivityLadderPriceValueObject getLadderPrice(String ladderPrice, Integer quantity) {
        if (StringUtils.isBlank(ladderPrice)) {
            log.info("阶梯价信息为空！");
            return null;
        }
        ActivityLadderPriceValueObject validLadder = null;
        try {
            List<ActivityLadderPriceValueObject> ladderPriceVOS = JSON.parseArray(ladderPrice, ActivityLadderPriceValueObject.class);
            if (CollectionUtils.isEmpty(ladderPriceVOS)) {
                log.info("阶梯价信息为空!");
                return null;
            }

            //按照件数倒序
            ladderPriceVOS = ladderPriceVOS.stream().sorted(Comparator.comparing(ActivityLadderPriceValueObject::getUnit).reversed())
                    .collect(Collectors.toList());

            //获取满足条件的第一个阶梯
            for (ActivityLadderPriceValueObject vo : ladderPriceVOS) {
                if (vo.getUnit() <= quantity) {
                    validLadder = vo;
                    break;
                }
            }
        } catch (Exception e) {
            log.warn("阶梯价转行异常，error:{}", JSON.toJSONString(e));
            return null;
        }
        return validLadder;
    }
}
