package net.summerfarm.manage.domain.merchant.repository;



import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuNotificationConfigEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantFrequentlyBuyingSkuNotificationConfigCommandParam;




/**
*
* <AUTHOR>
* @date 2025-05-22 14:24:47
* @version 1.0
*
*/
public interface MerchantFrequentlyBuyingSkuNotificationConfigCommandRepository {

    MerchantFrequentlyBuyingSkuNotificationConfigEntity insertSelective(MerchantFrequentlyBuyingSkuNotificationConfigCommandParam param);

    int updateSelectiveById(MerchantFrequentlyBuyingSkuNotificationConfigCommandParam param);

    int remove(Long id);

}