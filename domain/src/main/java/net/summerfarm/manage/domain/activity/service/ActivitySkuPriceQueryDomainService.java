package net.summerfarm.manage.domain.activity.service;


import net.summerfarm.manage.domain.activity.repository.ActivitySkuPriceQueryRepository;
import net.summerfarm.manage.domain.activity.repository.ActivitySkuPriceCommandRepository;
import net.summerfarm.manage.domain.activity.entity.ActivitySkuPriceEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 城市活动sku价格信息表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-04-09 14:55:30
 * @version 1.0
 *
 */
@Service
public class ActivitySkuPriceQueryDomainService {


}
