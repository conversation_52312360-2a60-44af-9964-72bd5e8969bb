package net.summerfarm.manage.domain.product.entity;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;


/**
 * <AUTHOR>
 * @date 2024-12-12 11:19:19
 * @version 1.0
 *
 */
@Data
@Slf4j
public class AppPopBiaoguoProductsDfEntity {
	/**
	 * 数据库唯一标识
	 */
	private Long id;

	/**
	 * 完整类目名称
	 */
	private String categoryName;

	/**
	 * 一级类目
	 */
	private String category1;

	/**
	 * 二级类目
	 */
	private String category2;

	/**
	 * 三级类目
	 */
	private String category3;

	/**
	 * 后台类目名称
	 */
	private String backCategoryName;

	/**
	 * 竞争对手
	 */
	private String competitor;

	/**
	 * SKU编码
	 */
	private String skuCode;

	/**
	 * 商品名称
	 */
	private String goodsName;

	/**
	 * 商品详情的描述
	 */
	private String babyName;

	/**
	 * 标准价格
	 */
	private String standardPrice;

	/**
	 * 最终标准价格
	 */
	private String finalStandardPrice;

	/**
	 * 上次标准价格
	 */
	private String lastTimeStandardPrice;

	/**
	 * 最终市斤价格
	 */
	private String finalUnitPriceCatty;

	/**
	 * 单位市斤价格
	 */
	private String unitPriceCatty;

	/**
	 * 商品类型
	 */
	private String goodsType;

	/**
	 * 规格
	 */
	private String specification;

	/**
	 * 单位
	 */
	private String unit;

	/**
	 * 毛重
	 */
	private String grossWeight;

	/**
	 * 净重
	 */
	private String netWeight;

	/**
	 * 月销量
	 */
	private String monthSale;

	/**
	 * 商品佣金比例
	 */
	private String goodsSiphonCommissionRate;

	/**
	 * 卖家佣金比例
	 */
	private String sellerSiphonCommissionRate;

	/**
	 * 卖家名称
	 */
	private String sellerName;

	/**
	 * 商品属性详情列表
	 */
	private String goodsPropDetailList;

	/**
	 * 商品链接
	 */
	private String url;

	/**
	 * 七天售后次数
	 */
	private String sevenDayAfterSale;

	/**
	 * 爬虫抓取时间
	 */
	private String spiderFetchTime;

	/**
	 * 商品创建时间(来自标果)
	 */
	private String createTime;

	/**
	 * 数据日期分区
	 */
	private String ds;

	/**
	 * 整件成本
	 */
	public BigDecimal getSkuCost() {
		try {
			BigDecimal salePrice = new BigDecimal(this.standardPrice);
			BigDecimal rate = new BigDecimal(this.goodsSiphonCommissionRate);
			BigDecimal ratePer = rate.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
			return salePrice.divide((ratePer.add(BigDecimal.ONE)), 2, RoundingMode.HALF_UP);
		} catch (Exception e) {
			log.warn("getSkuCost fail standardPrice:{}, goodsSiphonCommissionRate:{}", standardPrice, goodsSiphonCommissionRate);
			return BigDecimal.ZERO;
		}
	}

	/**
	 * 毛重单斤成本
	 */
	public BigDecimal getSkuGrossWeightCost() {
		try {
			BigDecimal salePrice = new BigDecimal(this.standardPrice);
			BigDecimal rate = new BigDecimal(this.goodsSiphonCommissionRate);
			BigDecimal grossWeight = new BigDecimal(this.grossWeight);
			BigDecimal ratePer = rate.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
			return salePrice.divide((ratePer.add(BigDecimal.ONE)), 2, RoundingMode.HALF_UP)
					.divide(grossWeight, 2, RoundingMode.HALF_UP);
		} catch (Exception e) {
			log.warn("getSkuCost fail standardPrice:{}, goodsSiphonCommissionRate:{}, grossWeight:{}", standardPrice, goodsSiphonCommissionRate, grossWeight);
			return BigDecimal.ZERO;
		}
	}
	
}