package net.summerfarm.manage.domain.merchant.repository;



import net.summerfarm.manage.domain.merchant.entity.MerchantFrequentlyBuyingSkuEntity;
import net.summerfarm.manage.domain.merchant.param.command.MerchantFrequentlyBuyingSkuCommandParam;




/**
*
* <AUTHOR>
* @date 2025-05-22 14:24:47
* @version 1.0
*
*/
public interface MerchantFrequentlyBuyingSkuCommandRepository {

    MerchantFrequentlyBuyingSkuEntity insertSelective(MerchantFrequentlyBuyingSkuCommandParam param);

    int updateSelectiveById(MerchantFrequentlyBuyingSkuCommandParam param);

    int remove(Long id);

}