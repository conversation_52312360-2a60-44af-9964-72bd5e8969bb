package net.summerfarm.manage.domain.product.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description
 * @Date 2024/12/18 13:49
 * @<AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExternalProductMappingRecordEntity {

    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 映射类型 1-sku 2-类目
     */
    private Integer type;

    /**
     * 鲜沐内部值
     */
    private String internalValue;

    /**
     * 外部平台值
     */
    private String externalValue;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 操作原因
     */
    private String operateReason;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;

}
