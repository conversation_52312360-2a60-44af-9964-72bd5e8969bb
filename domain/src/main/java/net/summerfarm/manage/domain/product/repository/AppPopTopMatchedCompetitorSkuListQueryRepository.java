package net.summerfarm.manage.domain.product.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.product.entity.AppPopTopMatchedCompetitorSkuListEntity;
import net.summerfarm.manage.domain.product.param.query.AppPopTopMatchedCompetitorSkuListQueryParam;



/**
*
* <AUTHOR>
* @date 2024-11-18 15:55:40
* @version 1.0
*
*/
public interface AppPopTopMatchedCompetitorSkuListQueryRepository {

    PageInfo<AppPopTopMatchedCompetitorSkuListEntity> getPage(AppPopTopMatchedCompetitorSkuListQueryParam param);

    AppPopTopMatchedCompetitorSkuListEntity selectById(Long id);

    List<AppPopTopMatchedCompetitorSkuListEntity> selectByCondition(AppPopTopMatchedCompetitorSkuListQueryParam param);

}