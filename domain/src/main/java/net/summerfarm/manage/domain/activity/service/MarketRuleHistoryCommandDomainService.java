package net.summerfarm.manage.domain.activity.service;


import net.summerfarm.manage.domain.activity.repository.MarketRuleHistoryQueryRepository;
import net.summerfarm.manage.domain.activity.repository.MarketRuleHistoryCommandRepository;
import net.summerfarm.manage.domain.activity.entity.MarketRuleHistoryEntity;
import net.summerfarm.manage.domain.activity.param.command.MarketRuleHistoryCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-05-31 17:39:38
 * @version 1.0
 *
 */
@Service
public class MarketRuleHistoryCommandDomainService {


    @Autowired
    private MarketRuleHistoryCommandRepository marketRuleHistoryCommandRepository;
    @Autowired
    private MarketRuleHistoryQueryRepository marketRuleHistoryQueryRepository;



    public MarketRuleHistoryEntity insert(MarketRuleHistoryCommandParam param) {
        return marketRuleHistoryCommandRepository.insertSelective(param);
    }


    public int update(MarketRuleHistoryCommandParam param) {
        return marketRuleHistoryCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return marketRuleHistoryCommandRepository.remove(id);
    }
}
