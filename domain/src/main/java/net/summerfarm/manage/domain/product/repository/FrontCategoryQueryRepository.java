package net.summerfarm.manage.domain.product.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.product.entity.FrontCategoryEntity;
import net.summerfarm.manage.domain.product.param.query.FrontCategoryQueryParam;



/**
*
* <AUTHOR>
* @date 2025-03-27 15:26:47
* @version 1.0
*
*/
public interface FrontCategoryQueryRepository {

    PageInfo<FrontCategoryEntity> getPage(FrontCategoryQueryParam param);

    FrontCategoryEntity selectById(Integer id);

    List<FrontCategoryEntity> selectByCondition(FrontCategoryQueryParam param);

    List<FrontCategoryEntity> selectAllPopCategory();

}