package net.summerfarm.manage.domain.area.repository;


import net.summerfarm.manage.domain.area.entity.Area;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import net.summerfarm.manage.domain.area.entity.AreaSimpleEntity;
import net.summerfarm.manage.domain.area.entity.LargeArea;

public interface AreaQueryRepository {
    List<Area> getNameByAreaNos(Collection<Integer> areaNos);

    Area selectByAreaNo(Integer areaNo);

    List<AreaSimpleEntity> batchQueryByAreaNos(List<Integer> areaNos);

    List<AreaSimpleEntity> batchQueryByAreaNames(Set<String> areaNames);

    List<AreaSimpleEntity> batchQueryByLargeAreaNos(Collection<Integer> largeAreaNos);

    List<Area> batchQueryAreaEntitiesByLargeAreaNos(Collection<Integer> largeAreaNos);

    /**
     * 查询所有生效的运营服务区
     * @return 运营服务区
     */
    List<Area> queryAllValidArea();

    /**
     * 查询全平台所有的运营大区
     * @param status, 可以为空，为空时会返回全部的数据（一次最多2000行）
     * @return 运营服务大区
     */
    List<LargeArea> queryAllLargeArea(Integer status);
    List<LargeArea> queryLargeAreaByLargeAreaNos(List<Integer> nos);
    /**
     * 查询关闭的运营区域
     */
    Set<Integer> queryCloseAreaNos();

    List<AreaSimpleEntity> batchQueryLargeAreaInfoByLargeAreaNos(List<Integer> largeAreaNos);
}
