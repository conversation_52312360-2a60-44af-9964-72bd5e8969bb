package net.summerfarm.manage.domain.admin.service;


import net.summerfarm.manage.domain.admin.repository.InvitecodeQueryRepository;
import net.summerfarm.manage.domain.admin.repository.InvitecodeCommandRepository;
import net.summerfarm.manage.domain.admin.entity.InvitecodeEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 地推码领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-06-18 13:07:30
 * @version 1.0
 *
 */
@Service
public class InvitecodeQueryDomainService {


}
