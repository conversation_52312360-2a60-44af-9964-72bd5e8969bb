package net.summerfarm.manage.domain.major.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.bigdecimal.BigDecimalStringConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/2/24 13:45
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuotationExcelDto {

    @ExcelProperty("序号")
    private Integer seqNo; // 序号可自动生成或手动维护

    @ExcelProperty("商品名称")
    private String pdName;

    @ExcelProperty("商品规格")
    private String specification;

    @ExcelProperty("产地")
    private String origin;

    @ExcelProperty("单位")
    private String unit;

    @ExcelProperty(value = "含税报价")
    private String price;

    @ExcelProperty("备注")
    private String remark;

    /**
     * 有效期
     */
    @ExcelIgnore()
    private String validPeriod;

    /**
     * 城市最高报价
     */
    @ExcelIgnore()
    private BigDecimal maxPrice;

    /**
     * 城市最低报价
     */
    @ExcelIgnore()
    private BigDecimal minPrice;

    /**
     * 大区编号
     */
    @ExcelIgnore()
    private Integer largeAreaNo;

    /**
     * 报价单的生效时间
     */
    @ExcelIgnore()
    private LocalDateTime validTime;

    /**
     * 报价单的失效时间
     */
    @ExcelIgnore()
    private LocalDateTime invalidTime;
}
