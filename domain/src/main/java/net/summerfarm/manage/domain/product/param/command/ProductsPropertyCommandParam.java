package net.summerfarm.manage.domain.product.param.command;

import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-04-30 18:26:17
 * @version 1.0
 *
 */
@Data
public class ProductsPropertyCommandParam {
	/**
	 * 主键、自增
	 */
	private Integer id;

	/**
	 * 属性名
	 */
	private String name;

	/**
	 * 属性类型：0、关键属性 1、销售属性
	 */
	private Integer type;

	/**
	 * 格式类型：0、复合型 1、数字+单位 2、选择字符串 3、自定义字符串
	 */
	private Integer formatType;

	/**
	 * 格式，根据类型不同处理，eg：
                            0/1:["g","kg"]
                            2:["越南","海南","台湾"]
	 */
	private String formatStr;

	/**
	 * 状态：0、失效 1、有效
	 */
	private Integer status;

	/**
	 * 创建人
	 */
	private String creator;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	

	
}