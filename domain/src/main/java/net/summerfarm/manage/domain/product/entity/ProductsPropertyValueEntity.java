package net.summerfarm.manage.domain.product.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: <EMAIL>
 * @create: 2024/1/26
 */
@Data
public class ProductsPropertyValueEntity implements Serializable {

    /**
     * 主键、自增
     */
    private Integer id;

    /**
     * pd_id
     */
    private Long pdId;

    /**
     * sku
     */
    private String sku;

    /**
     * 属性id
     */
    private Integer productsPropertyId;

    /**
     * 属性值
     */
    private String productsPropertyValue;

    /**
     * 属性名
     */
    private String name;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 属性类型：0、关键属性 1、销售属性
     */
    private Integer type;

    /**
     * 格式类型：0、复合型 1、区间+单位 2、数字+单位 3、选择字符串 4、自定义字符串
     */
    private Integer formatType;

    /**
     * 格式，根据类型不同处理，eg：
     0:[["g","kg"],["盒","箱"]]
     1/2:["g","kg"]
     3:["越南","海南","台湾"]
     */
    private String formatStr;

}
