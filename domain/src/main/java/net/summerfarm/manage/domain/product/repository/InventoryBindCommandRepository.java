package net.summerfarm.manage.domain.product.repository;



import net.summerfarm.manage.domain.product.entity.InventoryBindEntity;
import net.summerfarm.manage.domain.product.param.command.InventoryBindCommandParam;




/**
*
* <AUTHOR>
* @date 2024-05-06 13:48:40
* @version 1.0
*
*/
public interface InventoryBindCommandRepository {

    InventoryBindEntity insertSelective(InventoryBindCommandParam param);

    int updateSelectiveById(InventoryBindCommandParam param);

    int remove(Long id);

}