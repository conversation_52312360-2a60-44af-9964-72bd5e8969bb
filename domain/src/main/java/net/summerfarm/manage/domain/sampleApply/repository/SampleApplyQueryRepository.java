package net.summerfarm.manage.domain.sampleApply.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;

import net.summerfarm.manage.domain.sampleApply.entity.SampleApplyEntity;
import net.summerfarm.manage.domain.sampleApply.flatObject.SampleOrderFlatObject;
import net.summerfarm.manage.domain.sampleApply.param.query.SampleApplyQueryParam;



/**
*
* <AUTHOR>
* @date 2025-01-02 14:00:39
* @version 1.0
*
*/
public interface SampleApplyQueryRepository {

    PageInfo<SampleApplyEntity> getPage(SampleApplyQueryParam param);

    SampleApplyEntity selectById(Long sampleId);

    List<SampleApplyEntity> selectByCondition(SampleApplyQueryParam param);

    /**
     * 查询有效的样品单
     * @param sampleIdList 样品单ID
     * @return 结果
     */
    List<SampleOrderFlatObject> queryValidSampleOrderDeliveryDetail(List<String> sampleIdList);
}