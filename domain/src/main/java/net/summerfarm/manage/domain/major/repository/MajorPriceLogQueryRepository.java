package net.summerfarm.manage.domain.major.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.major.entity.MajorPriceLogEntity;
import net.summerfarm.manage.domain.major.param.query.MajorPriceLogQueryParam;



/**
*
* <AUTHOR>
* @date 2025-02-19 11:19:11
* @version 1.0
*
*/
public interface MajorPriceLogQueryRepository {

    PageInfo<MajorPriceLogEntity> getPage(MajorPriceLogQueryParam param);

    MajorPriceLogEntity selectById(Long id);

    List<MajorPriceLogEntity> selectByCondition(MajorPriceLogQueryParam param);

}