package net.summerfarm.manage.domain.activity.repository;



import net.summerfarm.manage.domain.activity.entity.ActivitySkuPriceEntity;
import net.summerfarm.manage.domain.activity.param.command.ActivitySkuPriceCommandParam;




/**
*
* <AUTHOR>
* @date 2024-04-09 14:55:30
* @version 1.0
*
*/
public interface ActivitySkuPriceCommandRepository {

    ActivitySkuPriceEntity insertSelective(ActivitySkuPriceCommandParam param);

    int updateSelectiveById(ActivitySkuPriceCommandParam param);

    int remove(Long id);

}