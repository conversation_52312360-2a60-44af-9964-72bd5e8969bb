package net.summerfarm.manage.domain.admin.repository;



import com.github.pagehelper.PageInfo;
import java.util.List;
import net.summerfarm.manage.domain.admin.entity.InvitecodeEntity;
import net.summerfarm.manage.domain.admin.param.query.InvitecodeQueryParam;



/**
*
* <AUTHOR>
* @date 2024-06-18 13:07:30
* @version 1.0
*
*/
public interface InvitecodeQueryRepository {

    PageInfo<InvitecodeEntity> getPage(InvitecodeQueryParam param);

    InvitecodeEntity selectById(Long inviteId);

    List<InvitecodeEntity> selectByCondition(InvitecodeQueryParam param);

}