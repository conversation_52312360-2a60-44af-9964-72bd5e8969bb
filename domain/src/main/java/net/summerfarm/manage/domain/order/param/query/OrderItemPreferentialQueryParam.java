package net.summerfarm.manage.domain.order.param.query;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import net.xianmu.common.input.BasePageInput;


/**
 * <AUTHOR>
 * @date 2024-05-31 15:06:17
 * @version 1.0
 *
 */
@Data
public class OrderItemPreferentialQueryParam extends BasePageInput {
	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 订单号
	 */
	private String orderNo;

	/**
	 * 优惠金额
	 */
	private BigDecimal amount;

	/**
	 * 订单项id
	 */
	private Long orderItemId;

	/**
	 * 优惠类型 0 活动 1组合包 2满减 3阶梯价 4黄金卡优惠 5预售尾款立减优惠 6秒杀 7搭配购 8多人拼团 9优惠券10赠品 11代仓 12预付 13红包
	 */
	private Integer type;

	/**
	 * type关联优惠id
	 */
	private Long relatedId;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 优惠明细快照-根据相应的优惠类型
	 */
	private String discountsDetailSnapshot;

	

	
}