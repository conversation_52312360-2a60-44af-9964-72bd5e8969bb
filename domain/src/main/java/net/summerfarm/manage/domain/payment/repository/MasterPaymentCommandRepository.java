package net.summerfarm.manage.domain.payment.repository;



import net.summerfarm.manage.domain.payment.entity.MasterPaymentEntity;
import net.summerfarm.manage.domain.payment.param.command.MasterPaymentCommandParam;




/**
*
* <AUTHOR>
* @date 2024-10-11 14:22:49
* @version 1.0
*
*/
public interface MasterPaymentCommandRepository {

    MasterPaymentEntity insertSelective(MasterPaymentCommandParam param);

    int updateSelectiveById(MasterPaymentCommandParam param);

    int remove(Long id);

}