package net.summerfarm.manage.domain.account.repository;



import net.summerfarm.manage.domain.account.entity.AccountChangeEntity;




/**
*
* <AUTHOR>
* @date 2023-10-26 16:20:19
* @version 1.0
*
*/
public interface AccountChangeCommandRepository {

    AccountChangeEntity insertSelective(AccountChangeEntity entity);

    int updateByIdSelective(AccountChangeEntity entity);

    int remove(Long id);

}