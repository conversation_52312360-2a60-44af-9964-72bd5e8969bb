package net.summerfarm.manage.domain.coupon.service;


import net.summerfarm.manage.domain.coupon.repository.CouponQueryRepository;
import net.summerfarm.manage.domain.coupon.repository.CouponCommandRepository;
import net.summerfarm.manage.domain.coupon.entity.CouponEntity;
import net.summerfarm.manage.domain.coupon.param.command.CouponCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 优惠券表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-12-19 13:49:12
 * @version 1.0
 *
 */
@Service
public class CouponCommandDomainService {


    @Autowired
    private CouponCommandRepository couponCommandRepository;
    @Autowired
    private CouponQueryRepository couponQueryRepository;



    public CouponEntity insert(CouponCommandParam param) {
        return couponCommandRepository.insertSelective(param);
    }


    public int update(CouponCommandParam param) {
        return couponCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return couponCommandRepository.remove(id);
    }
}
