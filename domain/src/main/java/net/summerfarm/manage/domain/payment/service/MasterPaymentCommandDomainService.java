package net.summerfarm.manage.domain.payment.service;


import net.summerfarm.manage.domain.payment.repository.MasterPaymentQueryRepository;
import net.summerfarm.manage.domain.payment.repository.MasterPaymentCommandRepository;
import net.summerfarm.manage.domain.payment.entity.MasterPaymentEntity;
import net.summerfarm.manage.domain.payment.param.command.MasterPaymentCommandParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



/**
 *
 * @Title: 主单支付表领域层
 * @Description:
 * <AUTHOR>
 * @date 2024-10-11 14:22:49
 * @version 1.0
 *
 */
@Service
public class MasterPaymentCommandDomainService {


    @Autowired
    private MasterPaymentCommandRepository masterPaymentCommandRepository;
    @Autowired
    private MasterPaymentQueryRepository masterPaymentQueryRepository;



    public MasterPaymentEntity insert(MasterPaymentCommandParam param) {
        return masterPaymentCommandRepository.insertSelective(param);
    }


    public int update(MasterPaymentCommandParam param) {
        return masterPaymentCommandRepository.updateSelectiveById(param);
    }


    public int delete(Long id) {
        return masterPaymentCommandRepository.remove(id);
    }
}
